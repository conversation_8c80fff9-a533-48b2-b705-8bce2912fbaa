import { jsx as _jsx, jsxs as _jsxs } from "react/jsx-runtime";
/**
 * @license
 * Copyright 2025 Google LLC
 * SPDX-License-Identifier: Apache-2.0
 */
import { useEffect, useState } from 'react';
import { Box, Text } from 'ink';
import { Colors } from '../colors.js';
import process from 'node:process';
import { formatMemoryUsage } from '../utils/formatters.js';
export const MemoryUsageDisplay = () => {
    const [memoryUsage, setMemoryUsage] = useState('');
    const [memoryUsageColor, setMemoryUsageColor] = useState(Colors.Gray);
    const [memoryIcon, setMemoryIcon] = useState('');
    useEffect(() => {
        const updateMemory = () => {
            const usage = process.memoryUsage().rss;
            const usageGB = usage / (1024 * 1024 * 1024);
            setMemoryUsage(formatMemoryUsage(usage));
            // Enhanced color and icon based on usage levels
            if (usageGB >= 2) {
                setMemoryUsageColor(Colors.AccentRed);
                setMemoryIcon('▲');
            }
            else if (usageGB >= 1) {
                setMemoryUsageColor(Colors.AccentYellow);
                setMemoryIcon('▶');
            }
            else {
                setMemoryUsageColor(Colors.Gray);
                setMemoryIcon('●');
            }
        };
        const intervalId = setInterval(updateMemory, 2000);
        updateMemory(); // Initial update
        return () => clearInterval(intervalId);
    }, []);
    return (_jsxs(Box, { children: [_jsx(Text, { color: Colors.Gray, children: "| " }), _jsxs(Text, { color: memoryUsageColor, children: [memoryIcon, " ", memoryUsage] })] }));
};
//# sourceMappingURL=MemoryUsageDisplay.js.map