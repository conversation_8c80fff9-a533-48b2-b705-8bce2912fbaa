/**
 * @license
 * Copyright 2025 Google LLC
 * SPDX-License-Identifier: Apache-2.0
 */
import { GaxiosError } from 'gaxios';
export function isNodeError(error) {
    return error instanceof Error && 'code' in error;
}
export function getErrorMessage(error) {
    if (error instanceof Error) {
        return error.message;
    }
    try {
        return String(error);
    }
    catch {
        return 'Failed to get error details';
    }
}
export class ForbiddenError extends Error {
}
export class UnauthorizedError extends Error {
}
export class BadRequestError extends Error {
}
export function toFriendlyError(error) {
    if (error instanceof GaxiosError) {
        const data = parseResponseData(error);
        if (data.error && data.error.message && data.error.code) {
            switch (data.error.code) {
                case 400:
                    return new BadRequestError(data.error.message);
                case 401:
                    return new UnauthorizedError(data.error.message);
                case 403:
                    // It's import to pass the message here since it might
                    // explain the cause like "the cloud project you're
                    // using doesn't have code assist enabled".
                    return new ForbiddenError(data.error.message);
                default:
            }
        }
    }
    return error;
}
function parseResponseData(error) {
    // Inexplicably, Gaxios sometimes doesn't JSONify the response data.
    if (typeof error.response?.data === 'string') {
        return JSON.parse(error.response?.data);
    }
    return typeof error.response?.data;
}
//# sourceMappingURL=errors.js.map