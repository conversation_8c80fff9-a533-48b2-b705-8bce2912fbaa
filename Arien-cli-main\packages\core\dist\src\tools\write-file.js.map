{"version": 3, "file": "write-file.js", "sourceRoot": "", "sources": ["../../../src/tools/write-file.ts"], "names": [], "mappings": "AAAA;;;;GAIG;AAEH,OAAO,EAAE,MAAM,IAAI,CAAC;AACpB,OAAO,IAAI,MAAM,MAAM,CAAC;AACxB,OAAO,KAAK,IAAI,MAAM,MAAM,CAAC;AAC7B,OAAO,EAAU,YAAY,EAAE,MAAM,qBAAqB,CAAC;AAC3D,OAAO,EACL,QAAQ,EAIR,uBAAuB,GAExB,MAAM,YAAY,CAAC;AACpB,OAAO,EAAE,eAAe,EAAE,MAAM,6BAA6B,CAAC;AAC9D,OAAO,EAAE,YAAY,EAAE,WAAW,EAAE,MAAM,mBAAmB,CAAC;AAC9D,OAAO,EAAE,eAAe,EAAE,WAAW,EAAE,MAAM,oBAAoB,CAAC;AAClE,OAAO,EACL,iBAAiB,EACjB,wBAAwB,GACzB,MAAM,2BAA2B,CAAC;AAEnC,OAAO,EAAE,oBAAoB,EAAE,MAAM,kBAAkB,CAAC;AAExD,OAAO,EAAE,mBAAmB,EAAE,MAAM,uBAAuB,CAAC;AAC5D,OAAO,EACL,yBAAyB,EACzB,aAAa,GACd,MAAM,yBAAyB,CAAC;AA6BjC;;GAEG;AACH,MAAM,OAAO,aACX,SAAQ,QAAyC;IAMpB;IAH7B,MAAM,CAAU,IAAI,GAAW,YAAY,CAAC;IAC3B,MAAM,CAAc;IAErC,YAA6B,MAAc;QACzC,KAAK,CACH,aAAa,CAAC,IAAI,EAClB,WAAW,EACX;;wGAEkG,EAClG;YACE,UAAU,EAAE;gBACV,SAAS,EAAE;oBACT,WAAW,EACT,oHAAoH;oBACtH,IAAI,EAAE,QAAQ;iBACf;gBACD,OAAO,EAAE;oBACP,WAAW,EAAE,mCAAmC;oBAChD,IAAI,EAAE,QAAQ;iBACf;aACF;YACD,QAAQ,EAAE,CAAC,WAAW,EAAE,SAAS,CAAC;YAClC,IAAI,EAAE,QAAQ;SACf,CACF,CAAC;QAtByB,WAAM,GAAN,MAAM,CAAQ;QAwBzC,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC,cAAc,EAAE,CAAC;IAC7C,CAAC;IAEO,YAAY,CAAC,WAAmB;QACtC,MAAM,cAAc,GAAG,IAAI,CAAC,SAAS,CAAC,WAAW,CAAC,CAAC;QACnD,MAAM,cAAc,GAAG,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,MAAM,CAAC,YAAY,EAAE,CAAC,CAAC;QAClE,MAAM,WAAW,GAAG,cAAc,CAAC,QAAQ,CAAC,IAAI,CAAC,GAAG,CAAC;YACnD,CAAC,CAAC,cAAc;YAChB,CAAC,CAAC,cAAc,GAAG,IAAI,CAAC,GAAG,CAAC;QAC9B,OAAO,CACL,cAAc,KAAK,cAAc;YACjC,cAAc,CAAC,UAAU,CAAC,WAAW,CAAC,CACvC,CAAC;IACJ,CAAC;IAED,kBAAkB,CAAC,MAA2B;QAC5C,IACE,IAAI,CAAC,MAAM,CAAC,UAAU;YACtB,CAAC,eAAe,CAAC,QAAQ,CACvB,IAAI,CAAC,MAAM,CAAC,UAAqC,EACjD,MAAM,CACP,EACD,CAAC;YACD,OAAO,sCAAsC,CAAC;QAChD,CAAC;QACD,MAAM,QAAQ,GAAG,MAAM,CAAC,SAAS,CAAC;QAClC,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC,QAAQ,CAAC,EAAE,CAAC;YAC/B,OAAO,+BAA+B,QAAQ,EAAE,CAAC;QACnD,CAAC;QACD,IAAI,CAAC,IAAI,CAAC,YAAY,CAAC,QAAQ,CAAC,EAAE,CAAC;YACjC,OAAO,gDAAgD,IAAI,CAAC,MAAM,CAAC,YAAY,EAAE,MAAM,QAAQ,EAAE,CAAC;QACpG,CAAC;QAED,IAAI,CAAC;YACH,0DAA0D;YAC1D,oEAAoE;YACpE,IAAI,EAAE,CAAC,UAAU,CAAC,QAAQ,CAAC,EAAE,CAAC;gBAC5B,MAAM,KAAK,GAAG,EAAE,CAAC,SAAS,CAAC,QAAQ,CAAC,CAAC;gBACrC,IAAI,KAAK,CAAC,WAAW,EAAE,EAAE,CAAC;oBACxB,OAAO,oCAAoC,QAAQ,EAAE,CAAC;gBACxD,CAAC;YACH,CAAC;QACH,CAAC;QAAC,OAAO,SAAkB,EAAE,CAAC;YAC5B,yGAAyG;YACzG,2EAA2E;YAC3E,OAAO,mDAAmD,QAAQ,aAAa,SAAS,YAAY,KAAK,CAAC,CAAC,CAAC,SAAS,CAAC,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC,SAAS,CAAC,EAAE,CAAC;QACtJ,CAAC;QAED,OAAO,IAAI,CAAC;IACd,CAAC;IAED,cAAc,CAAC,MAA2B;QACxC,IAAI,CAAC,MAAM,CAAC,SAAS,IAAI,CAAC,MAAM,CAAC,OAAO,EAAE,CAAC;YACzC,OAAO,4DAA4D,CAAC;QACtE,CAAC;QACD,MAAM,YAAY,GAAG,YAAY,CAC/B,MAAM,CAAC,SAAS,EAChB,IAAI,CAAC,MAAM,CAAC,YAAY,EAAE,CAC3B,CAAC;QACF,OAAO,cAAc,WAAW,CAAC,YAAY,CAAC,EAAE,CAAC;IACnD,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,oBAAoB,CACxB,MAA2B,EAC3B,WAAwB;QAExB,IAAI,IAAI,CAAC,MAAM,CAAC,eAAe,EAAE,KAAK,YAAY,CAAC,SAAS,EAAE,CAAC;YAC7D,OAAO,KAAK,CAAC;QACf,CAAC;QAED,MAAM,eAAe,GAAG,IAAI,CAAC,kBAAkB,CAAC,MAAM,CAAC,CAAC;QACxD,IAAI,eAAe,EAAE,CAAC;YACpB,OAAO,KAAK,CAAC;QACf,CAAC;QAED,MAAM,sBAAsB,GAAG,MAAM,IAAI,CAAC,wBAAwB,CAChE,MAAM,CAAC,SAAS,EAChB,MAAM,CAAC,OAAO,EACd,WAAW,CACZ,CAAC;QAEF,IAAI,sBAAsB,CAAC,KAAK,EAAE,CAAC;YACjC,8EAA8E;YAC9E,OAAO,KAAK,CAAC;QACf,CAAC;QAED,MAAM,EAAE,eAAe,EAAE,gBAAgB,EAAE,GAAG,sBAAsB,CAAC;QACrE,MAAM,YAAY,GAAG,YAAY,CAC/B,MAAM,CAAC,SAAS,EAChB,IAAI,CAAC,MAAM,CAAC,YAAY,EAAE,CAC3B,CAAC;QACF,MAAM,QAAQ,GAAG,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC;QAEjD,MAAM,QAAQ,GAAG,IAAI,CAAC,WAAW,CAC/B,QAAQ,EACR,eAAe,EAAE,qDAAqD;QACtE,gBAAgB,EAAE,qCAAqC;QACvD,SAAS,EACT,UAAU,EACV,oBAAoB,CACrB,CAAC;QAEF,MAAM,mBAAmB,GAAgC;YACvD,IAAI,EAAE,MAAM;YACZ,KAAK,EAAE,kBAAkB,WAAW,CAAC,YAAY,CAAC,EAAE;YACpD,QAAQ;YACR,QAAQ;YACR,SAAS,EAAE,KAAK,EAAE,OAAgC,EAAE,EAAE;gBACpD,IAAI,OAAO,KAAK,uBAAuB,CAAC,aAAa,EAAE,CAAC;oBACtD,IAAI,CAAC,MAAM,CAAC,eAAe,CAAC,YAAY,CAAC,SAAS,CAAC,CAAC;gBACtD,CAAC;YACH,CAAC;SACF,CAAC;QACF,OAAO,mBAAmB,CAAC;IAC7B,CAAC;IAED,KAAK,CAAC,OAAO,CACX,MAA2B,EAC3B,WAAwB;QAExB,MAAM,eAAe,GAAG,IAAI,CAAC,kBAAkB,CAAC,MAAM,CAAC,CAAC;QACxD,IAAI,eAAe,EAAE,CAAC;YACpB,OAAO;gBACL,UAAU,EAAE,+CAA+C,eAAe,EAAE;gBAC5E,aAAa,EAAE,UAAU,eAAe,EAAE;aAC3C,CAAC;QACJ,CAAC;QAED,MAAM,sBAAsB,GAAG,MAAM,IAAI,CAAC,wBAAwB,CAChE,MAAM,CAAC,SAAS,EAChB,MAAM,CAAC,OAAO,EACd,WAAW,CACZ,CAAC;QAEF,IAAI,sBAAsB,CAAC,KAAK,EAAE,CAAC;YACjC,MAAM,UAAU,GAAG,sBAAsB,CAAC,KAAK,CAAC;YAChD,MAAM,QAAQ,GAAG,iCAAiC,UAAU,CAAC,OAAO,EAAE,CAAC;YACvE,OAAO;gBACL,UAAU,EAAE,gCAAgC,MAAM,CAAC,SAAS,KAAK,UAAU,CAAC,OAAO,EAAE;gBACrF,aAAa,EAAE,QAAQ;aACxB,CAAC;QACJ,CAAC;QAED,MAAM,EACJ,eAAe,EACf,gBAAgB,EAAE,WAAW,EAC7B,UAAU,GACX,GAAG,sBAAsB,CAAC;QAC3B,mGAAmG;QACnG,0DAA0D;QAC1D,MAAM,SAAS,GACb,CAAC,UAAU;YACX,CAAC,sBAAsB,CAAC,KAAK,KAAK,SAAS;gBACzC,CAAC,sBAAsB,CAAC,UAAU,CAAC,CAAC;QAExC,IAAI,CAAC;YACH,MAAM,OAAO,GAAG,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC;YAC/C,IAAI,CAAC,EAAE,CAAC,UAAU,CAAC,OAAO,CAAC,EAAE,CAAC;gBAC5B,EAAE,CAAC,SAAS,CAAC,OAAO,EAAE,EAAE,SAAS,EAAE,IAAI,EAAE,CAAC,CAAC;YAC7C,CAAC;YAED,EAAE,CAAC,aAAa,CAAC,MAAM,CAAC,SAAS,EAAE,WAAW,EAAE,MAAM,CAAC,CAAC;YAExD,mCAAmC;YACnC,MAAM,QAAQ,GAAG,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC;YACjD,6EAA6E;YAC7E,iGAAiG;YACjG,sEAAsE;YACtE,MAAM,qBAAqB,GAAG,sBAAsB,CAAC,KAAK;gBACxD,CAAC,CAAC,EAAE,CAAC,0CAA0C;gBAC/C,CAAC,CAAC,eAAe,CAAC;YAEpB,MAAM,QAAQ,GAAG,IAAI,CAAC,WAAW,CAC/B,QAAQ,EACR,qBAAqB,EACrB,WAAW,EACX,UAAU,EACV,SAAS,EACT,oBAAoB,CACrB,CAAC;YAEF,MAAM,sBAAsB,GAAG;gBAC7B,SAAS;oBACP,CAAC,CAAC,+CAA+C,MAAM,CAAC,SAAS,GAAG;oBACpE,CAAC,CAAC,gCAAgC,MAAM,CAAC,SAAS,GAAG;aACxD,CAAC;YACF,IAAI,MAAM,CAAC,gBAAgB,EAAE,CAAC;gBAC5B,sBAAsB,CAAC,IAAI,CACzB,wCAAwC,MAAM,CAAC,OAAO,EAAE,CACzD,CAAC;YACJ,CAAC;YAED,MAAM,aAAa,GAAa,EAAE,QAAQ,EAAE,QAAQ,EAAE,CAAC;YAEvD,MAAM,KAAK,GAAG,WAAW,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,MAAM,CAAC;YAC7C,MAAM,QAAQ,GAAG,mBAAmB,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC;YACvD,MAAM,SAAS,GAAG,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC,CAAC,gBAAgB;YAClE,IAAI,SAAS,EAAE,CAAC;gBACd,yBAAyB,CACvB,IAAI,CAAC,MAAM,EACX,aAAa,CAAC,MAAM,EACpB,KAAK,EACL,QAAQ,EACR,SAAS,CACV,CAAC;YACJ,CAAC;iBAAM,CAAC;gBACN,yBAAyB,CACvB,IAAI,CAAC,MAAM,EACX,aAAa,CAAC,MAAM,EACpB,KAAK,EACL,QAAQ,EACR,SAAS,CACV,CAAC;YACJ,CAAC;YAED,OAAO;gBACL,UAAU,EAAE,sBAAsB,CAAC,IAAI,CAAC,GAAG,CAAC;gBAC5C,aAAa,EAAE,aAAa;aAC7B,CAAC;QACJ,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,MAAM,QAAQ,GAAG,0BAA0B,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC,EAAE,CAAC;YACpG,OAAO;gBACL,UAAU,EAAE,yBAAyB,MAAM,CAAC,SAAS,KAAK,QAAQ,EAAE;gBACpE,aAAa,EAAE,UAAU,QAAQ,EAAE;aACpC,CAAC;QACJ,CAAC;IACH,CAAC;IAEO,KAAK,CAAC,wBAAwB,CACpC,QAAgB,EAChB,eAAuB,EACvB,WAAwB;QAExB,IAAI,eAAe,GAAG,EAAE,CAAC;QACzB,IAAI,UAAU,GAAG,KAAK,CAAC;QACvB,IAAI,gBAAgB,GAAG,eAAe,CAAC;QAEvC,IAAI,CAAC;YACH,eAAe,GAAG,EAAE,CAAC,YAAY,CAAC,QAAQ,EAAE,MAAM,CAAC,CAAC;YACpD,UAAU,GAAG,IAAI,CAAC,CAAC,2BAA2B;QAChD,CAAC;QAAC,OAAO,GAAG,EAAE,CAAC;YACb,IAAI,WAAW,CAAC,GAAG,CAAC,IAAI,GAAG,CAAC,IAAI,KAAK,QAAQ,EAAE,CAAC;gBAC9C,UAAU,GAAG,KAAK,CAAC;gBACnB,eAAe,GAAG,EAAE,CAAC;YACvB,CAAC;iBAAM,CAAC;gBACN,wDAAwD;gBACxD,UAAU,GAAG,IAAI,CAAC,CAAC,mCAAmC;gBACtD,eAAe,GAAG,EAAE,CAAC,CAAC,wBAAwB;gBAC9C,MAAM,KAAK,GAAG;oBACZ,OAAO,EAAE,eAAe,CAAC,GAAG,CAAC;oBAC7B,IAAI,EAAE,WAAW,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,CAAC,SAAS;iBAC9C,CAAC;gBACF,wEAAwE;gBACxE,OAAO,EAAE,eAAe,EAAE,gBAAgB,EAAE,UAAU,EAAE,KAAK,EAAE,CAAC;YAClE,CAAC;QACH,CAAC;QAED,yCAAyC;QACzC,+EAA+E;QAC/E,2DAA2D;QAE3D,IAAI,UAAU,EAAE,CAAC;YACf,4CAA4C;YAC5C,MAAM,EAAE,MAAM,EAAE,eAAe,EAAE,GAAG,MAAM,iBAAiB,CACzD,eAAe,EACf;gBACE,UAAU,EAAE,eAAe,EAAE,6CAA6C;gBAC1E,UAAU,EAAE,eAAe;gBAC3B,SAAS,EAAE,QAAQ;aACpB,EACD,IAAI,CAAC,MAAM,EACX,WAAW,CACZ,CAAC;YACF,gBAAgB,GAAG,eAAe,CAAC,UAAU,CAAC;QAChD,CAAC;aAAM,CAAC;YACN,iCAAiC;YACjC,gBAAgB,GAAG,MAAM,wBAAwB,CAC/C,eAAe,EACf,IAAI,CAAC,MAAM,EACX,WAAW,CACZ,CAAC;QACJ,CAAC;QACD,OAAO,EAAE,eAAe,EAAE,gBAAgB,EAAE,UAAU,EAAE,CAAC;IAC3D,CAAC;IAED,gBAAgB,CACd,WAAwB;QAExB,OAAO;YACL,WAAW,EAAE,CAAC,MAA2B,EAAE,EAAE,CAAC,MAAM,CAAC,SAAS;YAC9D,iBAAiB,EAAE,KAAK,EAAE,MAA2B,EAAE,EAAE;gBACvD,MAAM,sBAAsB,GAAG,MAAM,IAAI,CAAC,wBAAwB,CAChE,MAAM,CAAC,SAAS,EAChB,MAAM,CAAC,OAAO,EACd,WAAW,CACZ,CAAC;gBACF,OAAO,sBAAsB,CAAC,eAAe,CAAC;YAChD,CAAC;YACD,kBAAkB,EAAE,KAAK,EAAE,MAA2B,EAAE,EAAE;gBACxD,MAAM,sBAAsB,GAAG,MAAM,IAAI,CAAC,wBAAwB,CAChE,MAAM,CAAC,SAAS,EAChB,MAAM,CAAC,OAAO,EACd,WAAW,CACZ,CAAC;gBACF,OAAO,sBAAsB,CAAC,gBAAgB,CAAC;YACjD,CAAC;YACD,mBAAmB,EAAE,CACnB,WAAmB,EACnB,uBAA+B,EAC/B,cAAmC,EACnC,EAAE,CAAC,CAAC;gBACJ,GAAG,cAAc;gBACjB,OAAO,EAAE,uBAAuB;gBAChC,gBAAgB,EAAE,IAAI;aACvB,CAAC;SACH,CAAC;IACJ,CAAC"}