{"version": 3, "file": "editCorrector.js", "sourceRoot": "", "sources": ["../../../src/utils/editCorrector.ts"], "names": [], "mappings": "AAAA;;;;GAIG;AAEH,OAAO,EAIL,IAAI,GACL,MAAM,eAAe,CAAC;AAGvB,OAAO,EAAE,QAAQ,EAAE,MAAM,eAAe,CAAC;AACzC,OAAO,EAAE,yBAAyB,EAAE,MAAM,qBAAqB,CAAC;AAEhE,MAAM,SAAS,GAAG,yBAAyB,CAAC;AAC5C,MAAM,UAAU,GAA0B;IACxC,cAAc,EAAE;QACd,cAAc,EAAE,CAAC;KAClB;CACF,CAAC;AAEF,MAAM,cAAc,GAAG,EAAE,CAAC;AAE1B,sCAAsC;AACtC,MAAM,mBAAmB,GAAG,IAAI,QAAQ,CACtC,cAAc,CACf,CAAC;AAEF,6CAA6C;AAC7C,MAAM,0BAA0B,GAAG,IAAI,QAAQ,CAAiB,cAAc,CAAC,CAAC;AAmBhF;;;;;;;;;;GAUG;AACH,MAAM,CAAC,KAAK,UAAU,iBAAiB,CACrC,cAAsB,EACtB,cAA8B,EAAE,iEAAiE;AACjG,MAAmB,EACnB,WAAwB;IAExB,MAAM,QAAQ,GAAG,GAAG,cAAc,MAAM,cAAc,CAAC,UAAU,MAAM,cAAc,CAAC,UAAU,EAAE,CAAC;IACnG,MAAM,YAAY,GAAG,mBAAmB,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC;IACvD,IAAI,YAAY,EAAE,CAAC;QACjB,OAAO,YAAY,CAAC;IACtB,CAAC;IAED,IAAI,cAAc,GAAG,cAAc,CAAC,UAAU,CAAC;IAC/C,MAAM,2BAA2B,GAC/B,yBAAyB,CAAC,cAAc,CAAC,UAAU,CAAC;QACpD,cAAc,CAAC,UAAU,CAAC;IAE5B,MAAM,oBAAoB,GAAG,cAAc,CAAC,qBAAqB,IAAI,CAAC,CAAC;IAEvE,IAAI,cAAc,GAAG,cAAc,CAAC,UAAU,CAAC;IAC/C,IAAI,WAAW,GAAG,gBAAgB,CAAC,cAAc,EAAE,cAAc,CAAC,CAAC;IAEnE,IAAI,WAAW,KAAK,oBAAoB,EAAE,CAAC;QACzC,IAAI,2BAA2B,EAAE,CAAC;YAChC,cAAc,GAAG,MAAM,wBAAwB,CAC7C,MAAM,EACN,cAAc,EACd,cAAc,CAAC,UAAU,EACzB,WAAW,CACZ,CAAC;QACJ,CAAC;IACH,CAAC;SAAM,IAAI,WAAW,GAAG,oBAAoB,EAAE,CAAC;QAC9C,MAAM,oBAAoB,GAAG,cAAc,CAAC,qBAAqB,IAAI,CAAC,CAAC;QAEvE,sDAAsD;QACtD,IAAI,WAAW,KAAK,oBAAoB,EAAE,CAAC;YACzC,MAAM,MAAM,GAAwB;gBAClC,MAAM,EAAE,EAAE,GAAG,cAAc,EAAE;gBAC7B,WAAW;aACZ,CAAC;YACF,mBAAmB,CAAC,GAAG,CAAC,QAAQ,EAAE,MAAM,CAAC,CAAC;YAC1C,OAAO,MAAM,CAAC;QAChB,CAAC;QAED,2EAA2E;QAC3E,IAAI,oBAAoB,KAAK,CAAC,EAAE,CAAC;YAC/B,MAAM,MAAM,GAAwB;gBAClC,MAAM,EAAE,EAAE,GAAG,cAAc,EAAE;gBAC7B,WAAW;aACZ,CAAC;YACF,mBAAmB,CAAC,GAAG,CAAC,QAAQ,EAAE,MAAM,CAAC,CAAC;YAC1C,OAAO,MAAM,CAAC;QAChB,CAAC;QAED,iFAAiF;QACjF,MAAM,MAAM,GAAwB;YAClC,MAAM,EAAE,EAAE,GAAG,cAAc,EAAE;YAC7B,WAAW;SACZ,CAAC;QACF,mBAAmB,CAAC,GAAG,CAAC,QAAQ,EAAE,MAAM,CAAC,CAAC;QAC1C,OAAO,MAAM,CAAC;IAChB,CAAC;SAAM,CAAC;QACN,4DAA4D;QAC5D,MAAM,yBAAyB,GAAG,yBAAyB,CACzD,cAAc,CAAC,UAAU,CAC1B,CAAC;QACF,WAAW,GAAG,gBAAgB,CAAC,cAAc,EAAE,yBAAyB,CAAC,CAAC;QAE1E,IAAI,WAAW,KAAK,oBAAoB,EAAE,CAAC;YACzC,cAAc,GAAG,yBAAyB,CAAC;YAC3C,IAAI,2BAA2B,EAAE,CAAC;gBAChC,cAAc,GAAG,MAAM,gBAAgB,CACrC,MAAM,EACN,cAAc,CAAC,UAAU,EAAE,eAAe;gBAC1C,yBAAyB,EAAE,gBAAgB;gBAC3C,cAAc,CAAC,UAAU,EAAE,8CAA8C;gBACzE,WAAW,CACZ,CAAC;YACJ,CAAC;QACH,CAAC;aAAM,IAAI,WAAW,KAAK,CAAC,EAAE,CAAC;YAC7B,MAAM,qBAAqB,GAAG,MAAM,wBAAwB,CAC1D,MAAM,EACN,cAAc,EACd,yBAAyB,EACzB,WAAW,CACZ,CAAC;YACF,MAAM,iBAAiB,GAAG,gBAAgB,CACxC,cAAc,EACd,qBAAqB,CACtB,CAAC;YAEF,IAAI,iBAAiB,KAAK,oBAAoB,EAAE,CAAC;gBAC/C,cAAc,GAAG,qBAAqB,CAAC;gBACvC,WAAW,GAAG,iBAAiB,CAAC;gBAEhC,IAAI,2BAA2B,EAAE,CAAC;oBAChC,MAAM,6BAA6B,GAAG,yBAAyB,CAC7D,cAAc,CAAC,UAAU,CAC1B,CAAC;oBACF,cAAc,GAAG,MAAM,gBAAgB,CACrC,MAAM,EACN,cAAc,CAAC,UAAU,EAAE,eAAe;oBAC1C,qBAAqB,EAAE,gBAAgB;oBACvC,6BAA6B,EAAE,0BAA0B;oBACzD,WAAW,CACZ,CAAC;gBACJ,CAAC;YACH,CAAC;iBAAM,CAAC;gBACN,4CAA4C;gBAC5C,MAAM,MAAM,GAAwB;oBAClC,MAAM,EAAE,EAAE,GAAG,cAAc,EAAE;oBAC7B,WAAW,EAAE,CAAC,EAAE,6BAA6B;iBAC9C,CAAC;gBACF,mBAAmB,CAAC,GAAG,CAAC,QAAQ,EAAE,MAAM,CAAC,CAAC;gBAC1C,OAAO,MAAM,CAAC;YAChB,CAAC;QACH,CAAC;aAAM,CAAC;YACN,oDAAoD;YACpD,MAAM,MAAM,GAAwB;gBAClC,MAAM,EAAE,EAAE,GAAG,cAAc,EAAE;gBAC7B,WAAW,EAAE,mBAAmB;aACjC,CAAC;YACF,mBAAmB,CAAC,GAAG,CAAC,QAAQ,EAAE,MAAM,CAAC,CAAC;YAC1C,OAAO,MAAM,CAAC;QAChB,CAAC;IACH,CAAC;IAED,MAAM,EAAE,YAAY,EAAE,IAAI,EAAE,GAAG,kBAAkB,CAC/C,cAAc,EACd,cAAc,EACd,cAAc,EACd,oBAAoB,CACrB,CAAC;IACF,cAAc,GAAG,YAAY,CAAC;IAC9B,cAAc,GAAG,IAAI,CAAC;IAEtB,4BAA4B;IAC5B,MAAM,MAAM,GAAwB;QAClC,MAAM,EAAE;YACN,SAAS,EAAE,cAAc,CAAC,SAAS;YACnC,UAAU,EAAE,cAAc;YAC1B,UAAU,EAAE,cAAc;SAC3B;QACD,WAAW,EAAE,gBAAgB,CAAC,cAAc,EAAE,cAAc,CAAC,EAAE,oDAAoD;KACpH,CAAC;IACF,mBAAmB,CAAC,GAAG,CAAC,QAAQ,EAAE,MAAM,CAAC,CAAC;IAC1C,OAAO,MAAM,CAAC;AAChB,CAAC;AAED,MAAM,CAAC,KAAK,UAAU,wBAAwB,CAC5C,OAAe,EACf,MAAmB,EACnB,WAAwB;IAExB,MAAM,YAAY,GAAG,0BAA0B,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC;IAC7D,IAAI,YAAY,EAAE,CAAC;QACjB,OAAO,YAAY,CAAC;IACtB,CAAC;IAED,MAAM,yBAAyB,GAC7B,yBAAyB,CAAC,OAAO,CAAC,KAAK,OAAO,CAAC;IACjD,IAAI,CAAC,yBAAyB,EAAE,CAAC;QAC/B,0BAA0B,CAAC,GAAG,CAAC,OAAO,EAAE,OAAO,CAAC,CAAC;QACjD,OAAO,OAAO,CAAC;IACjB,CAAC;IAED,MAAM,gBAAgB,GAAG,MAAM,qBAAqB,CAClD,OAAO,EACP,MAAM,EACN,WAAW,CACZ,CAAC;IACF,0BAA0B,CAAC,GAAG,CAAC,OAAO,EAAE,gBAAgB,CAAC,CAAC;IAC1D,OAAO,gBAAgB,CAAC;AAC1B,CAAC;AAED,iFAAiF;AACjF,MAAM,4BAA4B,GAAgB;IAChD,IAAI,EAAE,IAAI,CAAC,MAAM;IACjB,UAAU,EAAE;QACV,wBAAwB,EAAE;YACxB,IAAI,EAAE,IAAI,CAAC,MAAM;YACjB,WAAW,EACT,2HAA2H;SAC9H;KACF;IACD,QAAQ,EAAE,CAAC,0BAA0B,CAAC;CACvC,CAAC;AAEF,MAAM,CAAC,KAAK,UAAU,wBAAwB,CAC5C,WAAwB,EACxB,WAAmB,EACnB,kBAA0B,EAC1B,WAAwB;IAExB,MAAM,MAAM,GAAG;;;;;;;EAOf,kBAAkB;;;;;EAKlB,WAAW;;;6LAGgL,KAAK,WAAW,KAAK,oFAAoF,KAAK,WAAW,KAAK;;;;CAI1T,CAAC,IAAI,EAAE,CAAC;IAEP,MAAM,QAAQ,GAAc,CAAC,EAAE,IAAI,EAAE,MAAM,EAAE,KAAK,EAAE,CAAC,EAAE,IAAI,EAAE,MAAM,EAAE,CAAC,EAAE,CAAC,CAAC;IAE1E,IAAI,CAAC;QACH,MAAM,MAAM,GAAG,MAAM,WAAW,CAAC,YAAY,CAC3C,QAAQ,EACR,4BAA4B,EAC5B,WAAW,EACX,SAAS,EACT,UAAU,CACX,CAAC;QAEF,IACE,MAAM;YACN,OAAO,MAAM,CAAC,wBAAwB,KAAK,QAAQ;YACnD,MAAM,CAAC,wBAAwB,CAAC,MAAM,GAAG,CAAC,EAC1C,CAAC;YACD,OAAO,MAAM,CAAC,wBAAwB,CAAC;QACzC,CAAC;aAAM,CAAC;YACN,OAAO,kBAAkB,CAAC;QAC5B,CAAC;IACH,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACf,IAAI,WAAW,CAAC,OAAO,EAAE,CAAC;YACxB,MAAM,KAAK,CAAC;QACd,CAAC;QAED,OAAO,CAAC,KAAK,CACX,0DAA0D,EAC1D,KAAK,CACN,CAAC;QAEF,OAAO,kBAAkB,CAAC;IAC5B,CAAC;AACH,CAAC;AAED,6EAA6E;AAC7E,MAAM,4BAA4B,GAAgB;IAChD,IAAI,EAAE,IAAI,CAAC,MAAM;IACjB,UAAU,EAAE;QACV,oBAAoB,EAAE;YACpB,IAAI,EAAE,IAAI,CAAC,MAAM;YACjB,WAAW,EACT,kJAAkJ;SACrJ;KACF;IACD,QAAQ,EAAE,CAAC,sBAAsB,CAAC;CACnC,CAAC;AAEF;;GAEG;AACH,MAAM,CAAC,KAAK,UAAU,gBAAgB,CACpC,WAAwB,EACxB,iBAAyB,EACzB,kBAA0B,EAC1B,iBAAyB,EACzB,WAAwB;IAExB,IAAI,iBAAiB,KAAK,kBAAkB,EAAE,CAAC;QAC7C,OAAO,iBAAiB,CAAC;IAC3B,CAAC;IAED,MAAM,MAAM,GAAG;;;;;;EAMf,iBAAiB;;;;;EAKjB,kBAAkB;;;;;EAKlB,iBAAiB;;;;;4JAKyI,KAAK,WAAW,KAAK,oLAAoL,KAAK,wBAAwB,KAAK;;;;GAIpY,CAAC,IAAI,EAAE,CAAC;IAET,MAAM,QAAQ,GAAc,CAAC,EAAE,IAAI,EAAE,MAAM,EAAE,KAAK,EAAE,CAAC,EAAE,IAAI,EAAE,MAAM,EAAE,CAAC,EAAE,CAAC,CAAC;IAE1E,IAAI,CAAC;QACH,MAAM,MAAM,GAAG,MAAM,WAAW,CAAC,YAAY,CAC3C,QAAQ,EACR,4BAA4B,EAC5B,WAAW,EACX,SAAS,EACT,UAAU,CACX,CAAC;QAEF,IACE,MAAM;YACN,OAAO,MAAM,CAAC,oBAAoB,KAAK,QAAQ;YAC/C,MAAM,CAAC,oBAAoB,CAAC,MAAM,GAAG,CAAC,EACtC,CAAC;YACD,OAAO,MAAM,CAAC,oBAAoB,CAAC;QACrC,CAAC;aAAM,CAAC;YACN,OAAO,iBAAiB,CAAC;QAC3B,CAAC;IACH,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACf,IAAI,WAAW,CAAC,OAAO,EAAE,CAAC;YACxB,MAAM,KAAK,CAAC;QACd,CAAC;QAED,OAAO,CAAC,KAAK,CAAC,kDAAkD,EAAE,KAAK,CAAC,CAAC;QACzE,OAAO,iBAAiB,CAAC;IAC3B,CAAC;AACH,CAAC;AAED,MAAM,kCAAkC,GAAgB;IACtD,IAAI,EAAE,IAAI,CAAC,MAAM;IACjB,UAAU,EAAE;QACV,6BAA6B,EAAE;YAC7B,IAAI,EAAE,IAAI,CAAC,MAAM;YACjB,WAAW,EACT,sLAAsL;SACzL;KACF;IACD,QAAQ,EAAE,CAAC,+BAA+B,CAAC;CAC5C,CAAC;AAEF,MAAM,CAAC,KAAK,UAAU,wBAAwB,CAC5C,WAAwB,EACxB,SAAiB,EACjB,+BAAuC,EACvC,WAAwB;IAExB,MAAM,MAAM,GAAG;;;;;EAKf,SAAS;;;;;EAKT,+BAA+B;;;;;;;;;GAS9B,CAAC,IAAI,EAAE,CAAC;IAET,MAAM,QAAQ,GAAc,CAAC,EAAE,IAAI,EAAE,MAAM,EAAE,KAAK,EAAE,CAAC,EAAE,IAAI,EAAE,MAAM,EAAE,CAAC,EAAE,CAAC,CAAC;IAE1E,IAAI,CAAC;QACH,MAAM,MAAM,GAAG,MAAM,WAAW,CAAC,YAAY,CAC3C,QAAQ,EACR,kCAAkC,EAClC,WAAW,EACX,SAAS,EACT,UAAU,CACX,CAAC;QAEF,IACE,MAAM;YACN,OAAO,MAAM,CAAC,6BAA6B,KAAK,QAAQ;YACxD,MAAM,CAAC,6BAA6B,CAAC,MAAM,GAAG,CAAC,EAC/C,CAAC;YACD,OAAO,MAAM,CAAC,6BAA6B,CAAC;QAC9C,CAAC;aAAM,CAAC;YACN,OAAO,+BAA+B,CAAC;QACzC,CAAC;IACH,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACf,IAAI,WAAW,CAAC,OAAO,EAAE,CAAC;YACxB,MAAM,KAAK,CAAC;QACd,CAAC;QAED,OAAO,CAAC,KAAK,CACX,2DAA2D,EAC3D,KAAK,CACN,CAAC;QACF,OAAO,+BAA+B,CAAC;IACzC,CAAC;AACH,CAAC;AAED,MAAM,8BAA8B,GAAgB;IAClD,IAAI,EAAE,IAAI,CAAC,MAAM;IACjB,UAAU,EAAE;QACV,yBAAyB,EAAE;YACzB,IAAI,EAAE,IAAI,CAAC,MAAM;YACjB,WAAW,EACT,+IAA+I;SAClJ;KACF;IACD,QAAQ,EAAE,CAAC,2BAA2B,CAAC;CACxC,CAAC;AAEF,MAAM,CAAC,KAAK,UAAU,qBAAqB,CACzC,4BAAoC,EACpC,MAAmB,EACnB,WAAwB;IAExB,MAAM,MAAM,GAAG;;;;;EAKf,4BAA4B;;;;;;;;;GAS3B,CAAC,IAAI,EAAE,CAAC;IAET,MAAM,QAAQ,GAAc,CAAC,EAAE,IAAI,EAAE,MAAM,EAAE,KAAK,EAAE,CAAC,EAAE,IAAI,EAAE,MAAM,EAAE,CAAC,EAAE,CAAC,CAAC;IAE1E,IAAI,CAAC;QACH,MAAM,MAAM,GAAG,MAAM,MAAM,CAAC,YAAY,CACtC,QAAQ,EACR,8BAA8B,EAC9B,WAAW,EACX,SAAS,EACT,UAAU,CACX,CAAC;QAEF,IACE,MAAM;YACN,OAAO,MAAM,CAAC,yBAAyB,KAAK,QAAQ;YACpD,MAAM,CAAC,yBAAyB,CAAC,MAAM,GAAG,CAAC,EAC3C,CAAC;YACD,OAAO,MAAM,CAAC,yBAAyB,CAAC;QAC1C,CAAC;aAAM,CAAC;YACN,OAAO,4BAA4B,CAAC;QACtC,CAAC;IACH,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACf,IAAI,WAAW,CAAC,OAAO,EAAE,CAAC;YACxB,MAAM,KAAK,CAAC;QACd,CAAC;QAED,OAAO,CAAC,KAAK,CACX,uDAAuD,EACvD,KAAK,CACN,CAAC;QACF,OAAO,4BAA4B,CAAC;IACtC,CAAC;AACH,CAAC;AAED,SAAS,kBAAkB,CACzB,MAAc,EACd,iBAAyB,EACzB,cAAsB,EACtB,oBAA4B;IAE5B,MAAM,mBAAmB,GAAG,MAAM,CAAC,IAAI,EAAE,CAAC;IAC1C,IAAI,MAAM,CAAC,MAAM,KAAK,mBAAmB,CAAC,MAAM,EAAE,CAAC;QACjD,MAAM,wBAAwB,GAAG,gBAAgB,CAC/C,cAAc,EACd,mBAAmB,CACpB,CAAC;QAEF,IAAI,wBAAwB,KAAK,oBAAoB,EAAE,CAAC;YACtD,MAAM,qBAAqB,GAAG,iBAAiB,CAAC,IAAI,EAAE,CAAC;YACvD,OAAO;gBACL,YAAY,EAAE,mBAAmB;gBACjC,IAAI,EAAE,qBAAqB;aAC5B,CAAC;QACJ,CAAC;IACH,CAAC;IAED,OAAO;QACL,YAAY,EAAE,MAAM;QACpB,IAAI,EAAE,iBAAiB;KACxB,CAAC;AACJ,CAAC;AAED;;GAEG;AACH,MAAM,UAAU,yBAAyB,CAAC,WAAmB;IAC3D,qBAAqB;IACrB,wDAAwD;IACxD,oFAAoF;IACpF,kHAAkH;IAClH,mEAAmE;IACnE,+FAA+F;IAC/F,sFAAsF;IACtF,8FAA8F;IAC9F,+CAA+C;IAE/C,OAAO,WAAW,CAAC,OAAO,CACxB,yBAAyB,EACzB,CAAC,KAAK,EAAE,YAAY,EAAE,EAAE;QACtB,0GAA0G;QAC1G,+EAA+E;QAE/E,QAAQ,YAAY,EAAE,CAAC;YACrB,KAAK,GAAG;gBACN,OAAO,IAAI,CAAC,CAAC,4CAA4C;YAC3D,KAAK,GAAG;gBACN,OAAO,IAAI,CAAC,CAAC,wCAAwC;YACvD,KAAK,GAAG;gBACN,OAAO,IAAI,CAAC,CAAC,oDAAoD;YACnE,KAAK,GAAG;gBACN,OAAO,GAAG,CAAC,CAAC,8CAA8C;YAC5D,KAAK,GAAG;gBACN,OAAO,GAAG,CAAC,CAAC,kDAAkD;YAChE,KAAK,GAAG;gBACN,OAAO,GAAG,CAAC,CAAC,4CAA4C;YAC1D,KAAK,IAAI,EAAE,0DAA0D;gBACnE,OAAO,IAAI,CAAC,CAAC,iEAAiE;YAChF,KAAK,IAAI,EAAE,wDAAwD;gBACjE,OAAO,IAAI,CAAC,CAAC,qFAAqF;YACpG;gBACE,+EAA+E;gBAC/E,yFAAyF;gBACzF,OAAO,KAAK,CAAC;QACjB,CAAC;IACH,CAAC,CACF,CAAC;AACJ,CAAC;AAED;;GAEG;AACH,MAAM,UAAU,gBAAgB,CAAC,GAAW,EAAE,MAAc;IAC1D,IAAI,MAAM,KAAK,EAAE,EAAE,CAAC;QAClB,OAAO,CAAC,CAAC;IACX,CAAC;IACD,IAAI,KAAK,GAAG,CAAC,CAAC;IACd,IAAI,GAAG,GAAG,GAAG,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC;IAC9B,OAAO,GAAG,KAAK,CAAC,CAAC,EAAE,CAAC;QAClB,KAAK,EAAE,CAAC;QACR,GAAG,GAAG,GAAG,CAAC,OAAO,CAAC,MAAM,EAAE,GAAG,GAAG,MAAM,CAAC,MAAM,CAAC,CAAC,CAAC,uCAAuC;IACzF,CAAC;IACD,OAAO,KAAK,CAAC;AACf,CAAC;AAED,MAAM,UAAU,kCAAkC;IAChD,mBAAmB,CAAC,KAAK,EAAE,CAAC;IAC5B,0BAA0B,CAAC,KAAK,EAAE,CAAC;AACrC,CAAC"}