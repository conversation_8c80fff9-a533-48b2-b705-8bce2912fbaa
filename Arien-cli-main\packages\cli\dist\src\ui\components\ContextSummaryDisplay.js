import { jsx as _jsx, jsxs as _jsxs } from "react/jsx-runtime";
/**
 * @license
 * Copyright 2025 Google LLC
 * SPDX-License-Identifier: Apache-2.0
 */
import React from 'react';
import { Text } from 'ink';
import { Colors } from '../colors.js';
export const ContextSummaryDisplay = ({ arienMdFileCount, contextFileNames, mcpServers, showToolDescriptions, }) => {
    const mcpServerCount = Object.keys(mcpServers || {}).length;
    if (arienMdFileCount === 0 && mcpServerCount === 0) {
        return _jsx(Text, { color: Colors.Gray, children: "No context loaded" });
    }
    const contextElements = [];
    // Context files
    if (arienMdFileCount > 0) {
        const allNamesTheSame = new Set(contextFileNames).size < 2;
        const name = allNamesTheSame ? contextFileNames[0] : 'context';
        contextElements.push(_jsxs(Text, { color: Colors.AccentCyan, children: [arienMdFileCount, " ", name, " file", arienMdFileCount > 1 ? 's' : ''] }, "context"));
    }
    // MCP servers
    if (mcpServerCount > 0) {
        const mcpElement = (_jsxs(Text, { color: Colors.AccentPurple, children: [mcpServerCount, " MCP server", mcpServerCount > 1 ? 's' : '', mcpServers && Object.keys(mcpServers).length > 0 && (_jsx(Text, { color: Colors.Gray, dimColor: true, children: showToolDescriptions ? ' (Ctrl+T to hide)' : ' (Ctrl+T to view)' }))] }, "mcp"));
        contextElements.push(mcpElement);
    }
    return (_jsxs(Text, { children: [_jsx(Text, { color: Colors.Gray, children: "Context: " }), contextElements.map((element, index) => (_jsxs(React.Fragment, { children: [index > 0 && _jsx(Text, { color: Colors.Gray, children: " \u2022 " }), element] }, index)))] }));
};
//# sourceMappingURL=ContextSummaryDisplay.js.map