import { jsx as _jsx, jsxs as _jsxs } from "react/jsx-runtime";
import { Box, Text } from 'ink';
import { Colors } from '../colors.js';
export const ShellModeIndicator = () => (_jsxs(Box, { borderStyle: "round", borderColor: Colors.AccentYellow, paddingX: 1, children: [_jsx(Text, { color: Colors.AccentYellow, bold: true, children: "! Shell Mode" }), _jsx(Text, { color: Colors.Gray, dimColor: true, children: " (ESC to exit)" })] }));
//# sourceMappingURL=ShellModeIndicator.js.map