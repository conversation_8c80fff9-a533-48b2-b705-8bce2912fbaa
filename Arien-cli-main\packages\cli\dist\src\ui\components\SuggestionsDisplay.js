import { jsx as _jsx, jsxs as _jsxs } from "react/jsx-runtime";
/**
 * @license
 * Copyright 2025 Google LLC
 * SPDX-License-Identifier: Apache-2.0
 */
import { Box, Text } from 'ink';
import { Colors } from '../colors.js';
export const MAX_SUGGESTIONS_TO_SHOW = 8;
export function SuggestionsDisplay({ suggestions, activeIndex, isLoading, width, scrollOffset, userInput, }) {
    if (isLoading) {
        return (_jsx(Box, { paddingX: 1, width: width, borderStyle: "round", borderColor: Colors.Gray, children: _jsxs(Text, { color: Colors.Gray, children: [_jsx(Text, { color: Colors.AccentCyan, children: "\u25CE " }), "Loading suggestions..."] }) }));
    }
    if (suggestions.length === 0) {
        return null; // Don't render anything if there are no suggestions
    }
    // Calculate the visible slice based on scrollOffset
    const startIndex = scrollOffset;
    const endIndex = Math.min(scrollOffset + MAX_SUGGESTIONS_TO_SHOW, suggestions.length);
    const visibleSuggestions = suggestions.slice(startIndex, endIndex);
    return (_jsxs(Box, { flexDirection: "column", paddingX: 1, width: width, borderStyle: "round", borderColor: Colors.AccentCyan, children: [scrollOffset > 0 && (_jsx(Box, { marginBottom: 0, children: _jsx(Text, { color: Colors.AccentCyan, children: "\u25B2 More above" }) })), visibleSuggestions.map((suggestion, index) => {
                const originalIndex = startIndex + index;
                const isActive = originalIndex === activeIndex;
                const textColor = isActive ? Colors.AccentPurple : Colors.Gray;
                const prefix = isActive ? '→ ' : '  ';
                return (_jsx(Box, { width: width, children: _jsxs(Box, { flexDirection: "row", children: [_jsx(Text, { color: textColor, children: prefix }), userInput.startsWith('/') ? (
                            // only use box model for (/) command mode
                            _jsx(Box, { width: 20, flexShrink: 0, children: _jsx(Text, { color: textColor, bold: isActive, children: suggestion.label }) })) : (
                            // use regular text for other modes (@ context)
                            _jsx(Text, { color: textColor, bold: isActive, children: suggestion.label })), suggestion.description ? (_jsx(Box, { flexGrow: 1, marginLeft: 1, children: _jsx(Text, { color: textColor, dimColor: !isActive, wrap: "wrap", children: suggestion.description }) })) : null] }) }, `${suggestion}-${originalIndex}`));
            }), endIndex < suggestions.length && (_jsx(Box, { marginTop: 0, children: _jsx(Text, { color: Colors.AccentCyan, children: "\u25BC More below" }) })), suggestions.length > MAX_SUGGESTIONS_TO_SHOW && (_jsx(Box, { marginTop: 0, children: _jsxs(Text, { color: Colors.Gray, dimColor: true, children: [activeIndex + 1, " of ", suggestions.length, " \u00B7 Tab to select"] }) }))] }));
}
//# sourceMappingURL=SuggestionsDisplay.js.map