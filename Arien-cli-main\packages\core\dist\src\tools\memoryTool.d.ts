/**
 * @license
 * Copyright 2025 Google LLC
 * SPDX-License-Identifier: Apache-2.0
 */
import { BaseTool, ToolResult } from './tools.js';
export declare const ARIEN_CONFIG_DIR = ".arien";
export declare const DEFAULT_CONTEXT_FILENAME = "ARIEN.md";
export declare const MEMORY_SECTION_HEADER = "## Arien AI Added Memories";
export declare function setArienMdFilename(newFilename: string | string[]): void;
export declare function getCurrentArienMdFilename(): string;
export declare function getAllArienMdFilenames(): string[];
interface SaveMemoryParams {
    fact: string;
}
export declare class MemoryTool extends BaseTool<SaveMemoryParams, ToolResult> {
    static readonly Name: string;
    constructor();
    static performAddMemoryEntry(text: string, memoryFilePath: string, fsAdapter: {
        readFile: (path: string, encoding: 'utf-8') => Promise<string>;
        writeFile: (path: string, data: string, encoding: 'utf-8') => Promise<void>;
        mkdir: (path: string, options: {
            recursive: boolean;
        }) => Promise<string | undefined>;
    }): Promise<void>;
    execute(params: SaveMemoryParams, _signal: AbortSignal): Promise<ToolResult>;
}
export {};
