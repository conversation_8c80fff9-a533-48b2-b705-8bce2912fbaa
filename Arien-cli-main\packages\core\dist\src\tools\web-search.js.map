{"version": 3, "file": "web-search.js", "sourceRoot": "", "sources": ["../../../src/tools/web-search.ts"], "names": [], "mappings": "AAAA;;;;GAIG;AAGH,OAAO,EAAE,QAAQ,EAAc,MAAM,YAAY,CAAC;AAClD,OAAO,EAAE,eAAe,EAAE,MAAM,6BAA6B,CAAC;AAE9D,OAAO,EAAE,eAAe,EAAE,MAAM,oBAAoB,CAAC;AAErD,OAAO,EAAE,eAAe,EAAE,MAAM,8CAA8C,CAAC;AA4C/E;;GAEG;AACH,MAAM,OAAO,aAAc,SAAQ,QAGlC;IAG8B;IAF7B,MAAM,CAAU,IAAI,GAAW,mBAAmB,CAAC;IAEnD,YAA6B,MAAc;QACzC,KAAK,CACH,aAAa,CAAC,IAAI,EAClB,cAAc,EACd,yKAAyK,EACzK;YACE,IAAI,EAAE,QAAQ;YACd,UAAU,EAAE;gBACV,KAAK,EAAE;oBACL,IAAI,EAAE,QAAQ;oBACd,WAAW,EAAE,kDAAkD;iBAChE;aACF;YACD,QAAQ,EAAE,CAAC,OAAO,CAAC;SACpB,CACF,CAAC;QAfyB,WAAM,GAAN,MAAM,CAAQ;IAgB3C,CAAC;IAED,cAAc,CAAC,MAA2B;QACxC,IACE,IAAI,CAAC,MAAM,CAAC,UAAU;YACtB,CAAC,eAAe,CAAC,QAAQ,CACvB,IAAI,CAAC,MAAM,CAAC,UAAqC,EACjD,MAAM,CACP,EACD,CAAC;YACD,OAAO,kEAAkE,CAAC;QAC5E,CAAC;QACD,IAAI,CAAC,MAAM,CAAC,KAAK,IAAI,MAAM,CAAC,KAAK,CAAC,IAAI,EAAE,KAAK,EAAE,EAAE,CAAC;YAChD,OAAO,wCAAwC,CAAC;QAClD,CAAC;QACD,OAAO,IAAI,CAAC;IACd,CAAC;IAED,cAAc,CAAC,MAA2B;QACxC,OAAO,2BAA2B,MAAM,CAAC,KAAK,GAAG,CAAC;IACpD,CAAC;IAED,KAAK,CAAC,OAAO,CACX,MAA2B,EAC3B,MAAmB;QAEnB,MAAM,eAAe,GAAG,IAAI,CAAC,cAAc,CAAC,MAAM,CAAC,CAAC;QACpD,IAAI,eAAe,EAAE,CAAC;YACpB,OAAO;gBACL,UAAU,EAAE,+CAA+C,eAAe,EAAE;gBAC5E,aAAa,EAAE,eAAe;aAC/B,CAAC;QACJ,CAAC;QACD,MAAM,WAAW,GAAG,IAAI,CAAC,MAAM,CAAC,cAAc,EAAE,CAAC;QAEjD,IAAI,CAAC;YACH,MAAM,QAAQ,GAAG,MAAM,WAAW,CAAC,eAAe,CAChD,CAAC,EAAE,IAAI,EAAE,MAAM,EAAE,KAAK,EAAE,CAAC,EAAE,IAAI,EAAE,MAAM,CAAC,KAAK,EAAE,CAAC,EAAE,CAAC,EACnD,EAAE,KAAK,EAAE,CAAC,EAAE,YAAY,EAAE,EAAE,EAAE,CAAC,EAAE,EACjC,MAAM,CACP,CAAC;YAEF,MAAM,YAAY,GAAG,eAAe,CAAC,QAAQ,CAAC,CAAC;YAC/C,MAAM,iBAAiB,GAAG,QAAQ,CAAC,UAAU,EAAE,CAAC,CAAC,CAAC,EAAE,iBAAiB,CAAC;YACtE,MAAM,OAAO,GAAG,iBAAiB,EAAE,eAEtB,CAAC;YACd,MAAM,iBAAiB,GAAG,iBAAiB,EAAE,iBAEhC,CAAC;YAEd,IAAI,CAAC,YAAY,IAAI,CAAC,YAAY,CAAC,IAAI,EAAE,EAAE,CAAC;gBAC1C,OAAO;oBACL,UAAU,EAAE,sDAAsD,MAAM,CAAC,KAAK,GAAG;oBACjF,aAAa,EAAE,uBAAuB;iBACvC,CAAC;YACJ,CAAC;YAED,IAAI,oBAAoB,GAAG,YAAY,CAAC;YACxC,MAAM,mBAAmB,GAAa,EAAE,CAAC;YAEzC,IAAI,OAAO,IAAI,OAAO,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;gBAClC,OAAO,CAAC,OAAO,CAAC,CAAC,MAA0B,EAAE,KAAa,EAAE,EAAE;oBAC5D,MAAM,KAAK,GAAG,MAAM,CAAC,GAAG,EAAE,KAAK,IAAI,UAAU,CAAC;oBAC9C,MAAM,GAAG,GAAG,MAAM,CAAC,GAAG,EAAE,GAAG,IAAI,QAAQ,CAAC;oBACxC,mBAAmB,CAAC,IAAI,CAAC,IAAI,KAAK,GAAG,CAAC,KAAK,KAAK,KAAK,GAAG,GAAG,CAAC,CAAC;gBAC/D,CAAC,CAAC,CAAC;gBAEH,IAAI,iBAAiB,IAAI,iBAAiB,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;oBACtD,MAAM,UAAU,GAA6C,EAAE,CAAC;oBAChE,iBAAiB,CAAC,OAAO,CAAC,CAAC,OAA6B,EAAE,EAAE;wBAC1D,IAAI,OAAO,CAAC,OAAO,IAAI,OAAO,CAAC,qBAAqB,EAAE,CAAC;4BACrD,MAAM,cAAc,GAAG,OAAO,CAAC,qBAAqB;iCACjD,GAAG,CAAC,CAAC,UAAkB,EAAE,EAAE,CAAC,IAAI,UAAU,GAAG,CAAC,GAAG,CAAC;iCAClD,IAAI,CAAC,EAAE,CAAC,CAAC;4BACZ,UAAU,CAAC,IAAI,CAAC;gCACd,KAAK,EAAE,OAAO,CAAC,OAAO,CAAC,QAAQ;gCAC/B,MAAM,EAAE,cAAc;6BACvB,CAAC,CAAC;wBACL,CAAC;oBACH,CAAC,CAAC,CAAC;oBAEH,oFAAoF;oBACpF,UAAU,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,KAAK,GAAG,CAAC,CAAC,KAAK,CAAC,CAAC;oBAE7C,MAAM,aAAa,GAAG,oBAAoB,CAAC,KAAK,CAAC,EAAE,CAAC,CAAC,CAAC,mBAAmB;oBACzE,UAAU,CAAC,OAAO,CAAC,CAAC,SAAS,EAAE,EAAE;wBAC/B,8BAA8B;wBAC9B,aAAa,CAAC,MAAM,CAAC,SAAS,CAAC,KAAK,EAAE,CAAC,EAAE,SAAS,CAAC,MAAM,CAAC,CAAC;oBAC7D,CAAC,CAAC,CAAC;oBACH,oBAAoB,GAAG,aAAa,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC,CAAC,sCAAsC;gBACvF,CAAC;gBAED,IAAI,mBAAmB,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;oBACnC,oBAAoB;wBAClB,gBAAgB,GAAG,mBAAmB,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,6BAA6B;gBACpF,CAAC;YACH,CAAC;YAED,OAAO;gBACL,UAAU,EAAE,2BAA2B,MAAM,CAAC,KAAK,SAAS,oBAAoB,EAAE;gBAClF,aAAa,EAAE,uBAAuB,MAAM,CAAC,KAAK,aAAa;gBAC/D,OAAO;aACR,CAAC;QACJ,CAAC;QAAC,OAAO,KAAc,EAAE,CAAC;YACxB,MAAM,YAAY,GAAG,sCAAsC,MAAM,CAAC,KAAK,MAAM,eAAe,CAAC,KAAK,CAAC,EAAE,CAAC;YACtG,OAAO,CAAC,KAAK,CAAC,YAAY,EAAE,KAAK,CAAC,CAAC;YACnC,OAAO;gBACL,UAAU,EAAE,UAAU,YAAY,EAAE;gBACpC,aAAa,EAAE,8BAA8B;aAC9C,CAAC;QACJ,CAAC;IACH,CAAC"}