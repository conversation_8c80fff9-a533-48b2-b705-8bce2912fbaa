import { jsx as _jsx } from "react/jsx-runtime";
/**
 * @license
 * Copyright 2025 Google LLC
 * SPDX-License-Identifier: Apache-2.0
 */
import { useState, useEffect } from 'react';
import { Text } from 'ink';
import { Colors } from '../../colors.js';
export const AnimatedIcon = ({ isPending = false, color = Colors.AccentPurple, }) => {
    const [frameIndex, setFrameIndex] = useState(0);
    // Neural Network - Connecting nodes animation
    const animatedFrames = ['○─○', '●─○', '●─●', '●╱●', '●╲●', '●─●', '○─●', '○─○'];
    const staticIcon = '●●● ';
    useEffect(() => {
        if (!isPending)
            return;
        const interval = setInterval(() => {
            setFrameIndex((prev) => (prev + 1) % animatedFrames.length);
        }, 170); // 170ms for smooth neural network animation
        return () => clearInterval(interval);
    }, [isPending, animatedFrames.length]);
    return (_jsx(Text, { color: color, children: isPending ? animatedFrames[frameIndex] : staticIcon }));
};
//# sourceMappingURL=AnimatedIcon.js.map