{"version": 3, "file": "SuggestionsDisplay.js", "sourceRoot": "", "sources": ["../../../../src/ui/components/SuggestionsDisplay.tsx"], "names": [], "mappings": ";AAAA;;;;GAIG;AAEH,OAAO,EAAE,GAAG,EAAE,IAAI,EAAE,MAAM,KAAK,CAAC;AAChC,OAAO,EAAE,MAAM,EAAE,MAAM,cAAc,CAAC;AAetC,MAAM,CAAC,MAAM,uBAAuB,GAAG,CAAC,CAAC;AAEzC,MAAM,UAAU,kBAAkB,CAAC,EACjC,WAAW,EACX,WAAW,EACX,SAAS,EACT,KAAK,EACL,YAAY,EACZ,SAAS,GACe;IACxB,IAAI,SAAS,EAAE,CAAC;QACd,OAAO,CACL,KAAC,GAAG,IAAC,QAAQ,EAAE,CAAC,EAAE,KAAK,EAAE,KAAK,EAAE,WAAW,EAAC,OAAO,EAAC,WAAW,EAAE,MAAM,CAAC,IAAI,YAC1E,MAAC,IAAI,IAAC,KAAK,EAAE,MAAM,CAAC,IAAI,aACtB,KAAC,IAAI,IAAC,KAAK,EAAE,MAAM,CAAC,UAAU,wBAAW,8BAEpC,GACH,CACP,CAAC;IACJ,CAAC;IAED,IAAI,WAAW,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;QAC7B,OAAO,IAAI,CAAC,CAAC,oDAAoD;IACnE,CAAC;IAED,oDAAoD;IACpD,MAAM,UAAU,GAAG,YAAY,CAAC;IAChC,MAAM,QAAQ,GAAG,IAAI,CAAC,GAAG,CACvB,YAAY,GAAG,uBAAuB,EACtC,WAAW,CAAC,MAAM,CACnB,CAAC;IACF,MAAM,kBAAkB,GAAG,WAAW,CAAC,KAAK,CAAC,UAAU,EAAE,QAAQ,CAAC,CAAC;IAEnE,OAAO,CACL,MAAC,GAAG,IAAC,aAAa,EAAC,QAAQ,EAAC,QAAQ,EAAE,CAAC,EAAE,KAAK,EAAE,KAAK,EAAE,WAAW,EAAC,OAAO,EAAC,WAAW,EAAE,MAAM,CAAC,UAAU,aACtG,YAAY,GAAG,CAAC,IAAI,CACnB,KAAC,GAAG,IAAC,YAAY,EAAE,CAAC,YAClB,KAAC,IAAI,IAAC,KAAK,EAAE,MAAM,CAAC,UAAU,kCAAqB,GAC/C,CACP,EAEA,kBAAkB,CAAC,GAAG,CAAC,CAAC,UAAU,EAAE,KAAK,EAAE,EAAE;gBAC5C,MAAM,aAAa,GAAG,UAAU,GAAG,KAAK,CAAC;gBACzC,MAAM,QAAQ,GAAG,aAAa,KAAK,WAAW,CAAC;gBAC/C,MAAM,SAAS,GAAG,QAAQ,CAAC,CAAC,CAAC,MAAM,CAAC,YAAY,CAAC,CAAC,CAAC,MAAM,CAAC,IAAI,CAAC;gBAC/D,MAAM,MAAM,GAAG,QAAQ,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC;gBAEtC,OAAO,CACL,KAAC,GAAG,IAAwC,KAAK,EAAE,KAAK,YACtD,MAAC,GAAG,IAAC,aAAa,EAAC,KAAK,aACtB,KAAC,IAAI,IAAC,KAAK,EAAE,SAAS,YAAG,MAAM,GAAQ,EACtC,SAAS,CAAC,UAAU,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;4BAC3B,0CAA0C;4BAC1C,KAAC,GAAG,IAAC,KAAK,EAAE,EAAE,EAAE,UAAU,EAAE,CAAC,YAC3B,KAAC,IAAI,IAAC,KAAK,EAAE,SAAS,EAAE,IAAI,EAAE,QAAQ,YAAG,UAAU,CAAC,KAAK,GAAQ,GAC7D,CACP,CAAC,CAAC,CAAC;4BACF,+CAA+C;4BAC/C,KAAC,IAAI,IAAC,KAAK,EAAE,SAAS,EAAE,IAAI,EAAE,QAAQ,YAAG,UAAU,CAAC,KAAK,GAAQ,CAClE,EACA,UAAU,CAAC,WAAW,CAAC,CAAC,CAAC,CACxB,KAAC,GAAG,IAAC,QAAQ,EAAE,CAAC,EAAE,UAAU,EAAE,CAAC,YAC7B,KAAC,IAAI,IAAC,KAAK,EAAE,SAAS,EAAE,QAAQ,EAAE,CAAC,QAAQ,EAAE,IAAI,EAAC,MAAM,YACrD,UAAU,CAAC,WAAW,GAClB,GACH,CACP,CAAC,CAAC,CAAC,IAAI,IACJ,IAnBE,GAAG,UAAU,IAAI,aAAa,EAAE,CAoBpC,CACP,CAAC;YACJ,CAAC,CAAC,EACD,QAAQ,GAAG,WAAW,CAAC,MAAM,IAAI,CAChC,KAAC,GAAG,IAAC,SAAS,EAAE,CAAC,YACf,KAAC,IAAI,IAAC,KAAK,EAAE,MAAM,CAAC,UAAU,kCAAqB,GAC/C,CACP,EACA,WAAW,CAAC,MAAM,GAAG,uBAAuB,IAAI,CAC/C,KAAC,GAAG,IAAC,SAAS,EAAE,CAAC,YACf,MAAC,IAAI,IAAC,KAAK,EAAE,MAAM,CAAC,IAAI,EAAE,QAAQ,mBAC/B,WAAW,GAAG,CAAC,UAAM,WAAW,CAAC,MAAM,6BACnC,GACH,CACP,IACG,CACP,CAAC;AACJ,CAAC"}