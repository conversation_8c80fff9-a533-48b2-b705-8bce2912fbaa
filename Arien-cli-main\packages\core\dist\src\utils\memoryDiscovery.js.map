{"version": 3, "file": "memoryDiscovery.js", "sourceRoot": "", "sources": ["../../../src/utils/memoryDiscovery.ts"], "names": [], "mappings": "AAAA;;;;GAIG;AAEH,OAAO,KAAK,EAAE,MAAM,aAAa,CAAC;AAClC,OAAO,KAAK,MAAM,MAAM,IAAI,CAAC;AAC7B,OAAO,KAAK,IAAI,MAAM,MAAM,CAAC;AAC7B,OAAO,EAAE,OAAO,EAAE,MAAM,IAAI,CAAC;AAC7B,OAAO,EAAE,aAAa,EAAE,MAAM,oBAAoB,CAAC;AACnD,OAAO,EACL,gBAAgB,EAChB,sBAAsB,GACvB,MAAM,wBAAwB,CAAC;AAGhC,0EAA0E;AAC1E,kFAAkF;AAClF,MAAM,MAAM,GAAG;IACb,8DAA8D;IAC9D,KAAK,EAAE,CAAC,GAAG,IAAW,EAAE,EAAE,CACxB,OAAO,CAAC,KAAK,CAAC,2BAA2B,EAAE,GAAG,IAAI,CAAC;IACrD,8DAA8D;IAC9D,IAAI,EAAE,CAAC,GAAG,IAAW,EAAE,EAAE,CAAC,OAAO,CAAC,IAAI,CAAC,0BAA0B,EAAE,GAAG,IAAI,CAAC;IAC3E,8DAA8D;IAC9D,KAAK,EAAE,CAAC,GAAG,IAAW,EAAE,EAAE,CACxB,OAAO,CAAC,KAAK,CAAC,2BAA2B,EAAE,GAAG,IAAI,CAAC;CACtD,CAAC;AAEF,MAAM,kCAAkC,GAAG,GAAG,CAAC;AAO/C,KAAK,UAAU,eAAe,CAAC,QAAgB;IAC7C,IAAI,UAAU,GAAG,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC;IACxC,OAAO,IAAI,EAAE,CAAC;QACZ,MAAM,OAAO,GAAG,IAAI,CAAC,IAAI,CAAC,UAAU,EAAE,MAAM,CAAC,CAAC;QAC9C,IAAI,CAAC;YACH,MAAM,KAAK,GAAG,MAAM,EAAE,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;YACrC,IAAI,KAAK,CAAC,WAAW,EAAE,EAAE,CAAC;gBACxB,OAAO,UAAU,CAAC;YACpB,CAAC;QACH,CAAC;QAAC,OAAO,KAAc,EAAE,CAAC;YACxB,sEAAsE;YACtE,yEAAyE;YACzE,MAAM,QAAQ,GACZ,OAAO,KAAK,KAAK,QAAQ;gBACzB,KAAK,KAAK,IAAI;gBACd,MAAM,IAAI,KAAK;gBACd,KAA0B,CAAC,IAAI,KAAK,QAAQ,CAAC;YAEhD,sDAAsD;YACtD,uEAAuE;YACvE,MAAM,SAAS,GAAG,OAAO,CAAC,GAAG,CAAC,QAAQ,KAAK,MAAM,IAAI,OAAO,CAAC,GAAG,CAAC,MAAM,CAAC;YAExE,IAAI,CAAC,QAAQ,IAAI,CAAC,SAAS,EAAE,CAAC;gBAC5B,IAAI,OAAO,KAAK,KAAK,QAAQ,IAAI,KAAK,KAAK,IAAI,IAAI,MAAM,IAAI,KAAK,EAAE,CAAC;oBACnE,MAAM,OAAO,GAAG,KAA0C,CAAC;oBAC3D,MAAM,CAAC,IAAI,CACT,wCAAwC,OAAO,KAAK,OAAO,CAAC,OAAO,EAAE,CACtE,CAAC;gBACJ,CAAC;qBAAM,CAAC;oBACN,MAAM,CAAC,IAAI,CACT,qDAAqD,OAAO,KAAK,MAAM,CAAC,KAAK,CAAC,EAAE,CACjF,CAAC;gBACJ,CAAC;YACH,CAAC;QACH,CAAC;QACD,MAAM,SAAS,GAAG,IAAI,CAAC,OAAO,CAAC,UAAU,CAAC,CAAC;QAC3C,IAAI,SAAS,KAAK,UAAU,EAAE,CAAC;YAC7B,OAAO,IAAI,CAAC;QACd,CAAC;QACD,UAAU,GAAG,SAAS,CAAC;IACzB,CAAC;AACH,CAAC;AAED,KAAK,UAAU,2BAA2B,CACxC,uBAA+B,EAC/B,YAAoB,EACpB,SAAkB,EAClB,WAAiC,EACjC,4BAAsC,EAAE;IAExC,MAAM,QAAQ,GAAG,IAAI,GAAG,EAAU,CAAC;IACnC,MAAM,gBAAgB,GAAG,sBAAsB,EAAE,CAAC;IAElD,KAAK,MAAM,eAAe,IAAI,gBAAgB,EAAE,CAAC;QAC/C,MAAM,WAAW,GAAG,IAAI,CAAC,OAAO,CAAC,uBAAuB,CAAC,CAAC;QAC1D,MAAM,YAAY,GAAG,IAAI,CAAC,OAAO,CAAC,YAAY,CAAC,CAAC;QAChD,MAAM,gBAAgB,GAAG,IAAI,CAAC,IAAI,CAChC,YAAY,EACZ,gBAAgB,EAChB,eAAe,CAChB,CAAC;QAEF,IAAI,SAAS;YACX,MAAM,CAAC,KAAK,CACV,iBAAiB,eAAe,uBAAuB,WAAW,EAAE,CACrE,CAAC;QACJ,IAAI,SAAS;YAAE,MAAM,CAAC,KAAK,CAAC,wBAAwB,YAAY,EAAE,CAAC,CAAC;QAEpE,IAAI,CAAC;YACH,MAAM,EAAE,CAAC,MAAM,CAAC,gBAAgB,EAAE,MAAM,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC;YACzD,QAAQ,CAAC,GAAG,CAAC,gBAAgB,CAAC,CAAC;YAC/B,IAAI,SAAS;gBACX,MAAM,CAAC,KAAK,CACV,yBAAyB,eAAe,KAAK,gBAAgB,EAAE,CAChE,CAAC;QACN,CAAC;QAAC,MAAM,CAAC;YACP,IAAI,SAAS;gBACX,MAAM,CAAC,KAAK,CACV,UAAU,eAAe,+BAA+B,gBAAgB,EAAE,CAC3E,CAAC;QACN,CAAC;QAED,MAAM,WAAW,GAAG,MAAM,eAAe,CAAC,WAAW,CAAC,CAAC;QACvD,IAAI,SAAS;YACX,MAAM,CAAC,KAAK,CAAC,4BAA4B,WAAW,IAAI,MAAM,EAAE,CAAC,CAAC;QAEpE,MAAM,WAAW,GAAa,EAAE,CAAC;QACjC,IAAI,UAAU,GAAG,WAAW,CAAC;QAC7B,wFAAwF;QACxF,MAAM,eAAe,GAAG,WAAW;YACjC,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,WAAW,CAAC;YAC3B,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,YAAY,CAAC,CAAC;QAE/B,OAAO,UAAU,IAAI,UAAU,KAAK,IAAI,CAAC,OAAO,CAAC,UAAU,CAAC,EAAE,CAAC;YAC7D,oDAAoD;YACpD,IAAI,SAAS,EAAE,CAAC;gBACd,MAAM,CAAC,KAAK,CACV,gBAAgB,eAAe,sBAAsB,UAAU,EAAE,CAClE,CAAC;YACJ,CAAC;YAED,uEAAuE;YACvE,wDAAwD;YACxD,IAAI,UAAU,KAAK,IAAI,CAAC,IAAI,CAAC,YAAY,EAAE,gBAAgB,CAAC,EAAE,CAAC;gBAC7D,IAAI,SAAS,EAAE,CAAC;oBACd,MAAM,CAAC,KAAK,CACV,4EAA4E,UAAU,EAAE,CACzF,CAAC;gBACJ,CAAC;gBACD,MAAM;YACR,CAAC;YAED,MAAM,aAAa,GAAG,IAAI,CAAC,IAAI,CAAC,UAAU,EAAE,eAAe,CAAC,CAAC;YAC7D,IAAI,CAAC;gBACH,MAAM,EAAE,CAAC,MAAM,CAAC,aAAa,EAAE,MAAM,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC;gBACtD,yEAAyE;gBACzE,IAAI,aAAa,KAAK,gBAAgB,EAAE,CAAC;oBACvC,WAAW,CAAC,OAAO,CAAC,aAAa,CAAC,CAAC;oBACnC,IAAI,SAAS,EAAE,CAAC;wBACd,MAAM,CAAC,KAAK,CACV,yBAAyB,eAAe,KAAK,aAAa,EAAE,CAC7D,CAAC;oBACJ,CAAC;gBACH,CAAC;YACH,CAAC;YAAC,MAAM,CAAC;gBACP,IAAI,SAAS,EAAE,CAAC;oBACd,MAAM,CAAC,KAAK,CACV,UAAU,eAAe,kCAAkC,UAAU,EAAE,CACxE,CAAC;gBACJ,CAAC;YACH,CAAC;YAED,oFAAoF;YACpF,IAAI,UAAU,KAAK,eAAe,EAAE,CAAC;gBACnC,IAAI,SAAS;oBACX,MAAM,CAAC,KAAK,CACV,oDAAoD,UAAU,EAAE,CACjE,CAAC;gBACJ,MAAM;YACR,CAAC;YAED,UAAU,GAAG,IAAI,CAAC,OAAO,CAAC,UAAU,CAAC,CAAC;QACxC,CAAC;QACD,WAAW,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;QAE5C,MAAM,aAAa,GAAG,MAAM,aAAa,CAAC,WAAW,EAAE;YACrD,QAAQ,EAAE,eAAe;YACzB,OAAO,EAAE,kCAAkC;YAC3C,KAAK,EAAE,SAAS;YAChB,WAAW;SACZ,CAAC,CAAC;QACH,aAAa,CAAC,IAAI,EAAE,CAAC,CAAC,uEAAuE;QAC7F,IAAI,SAAS,IAAI,aAAa,CAAC,MAAM,GAAG,CAAC;YACvC,MAAM,CAAC,KAAK,CACV,kBAAkB,eAAe,oBAAoB,IAAI,CAAC,SAAS,CACjE,aAAa,CACd,EAAE,CACJ,CAAC;QACJ,wFAAwF;QACxF,KAAK,MAAM,KAAK,IAAI,aAAa,EAAE,CAAC;YAClC,QAAQ,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC;QACtB,CAAC;IACH,CAAC;IAED,mCAAmC;IACnC,KAAK,MAAM,aAAa,IAAI,yBAAyB,EAAE,CAAC;QACtD,QAAQ,CAAC,GAAG,CAAC,aAAa,CAAC,CAAC;IAC9B,CAAC;IAED,MAAM,UAAU,GAAG,KAAK,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;IAExC,IAAI,SAAS;QACX,MAAM,CAAC,KAAK,CACV,iBAAiB,sBAAsB,EAAE,mBAAmB,IAAI,CAAC,SAAS,CACxE,UAAU,CACX,EAAE,CACJ,CAAC;IACJ,OAAO,UAAU,CAAC;AACpB,CAAC;AAED,KAAK,UAAU,gBAAgB,CAC7B,SAAmB,EACnB,SAAkB;IAElB,MAAM,OAAO,GAAuB,EAAE,CAAC;IACvC,KAAK,MAAM,QAAQ,IAAI,SAAS,EAAE,CAAC;QACjC,IAAI,CAAC;YACH,MAAM,OAAO,GAAG,MAAM,EAAE,CAAC,QAAQ,CAAC,QAAQ,EAAE,OAAO,CAAC,CAAC;YACrD,OAAO,CAAC,IAAI,CAAC,EAAE,QAAQ,EAAE,OAAO,EAAE,CAAC,CAAC;YACpC,IAAI,SAAS;gBACX,MAAM,CAAC,KAAK,CACV,sBAAsB,QAAQ,aAAa,OAAO,CAAC,MAAM,GAAG,CAC7D,CAAC;QACN,CAAC;QAAC,OAAO,KAAc,EAAE,CAAC;YACxB,MAAM,SAAS,GAAG,OAAO,CAAC,GAAG,CAAC,QAAQ,KAAK,MAAM,IAAI,OAAO,CAAC,GAAG,CAAC,MAAM,CAAC;YACxE,IAAI,CAAC,SAAS,EAAE,CAAC;gBACf,MAAM,OAAO,GAAG,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;gBACvE,MAAM,CAAC,IAAI,CACT,2BAA2B,sBAAsB,EAAE,YAAY,QAAQ,YAAY,OAAO,EAAE,CAC7F,CAAC;YACJ,CAAC;YACD,OAAO,CAAC,IAAI,CAAC,EAAE,QAAQ,EAAE,OAAO,EAAE,IAAI,EAAE,CAAC,CAAC,CAAC,qCAAqC;YAChF,IAAI,SAAS;gBAAE,MAAM,CAAC,KAAK,CAAC,mBAAmB,QAAQ,EAAE,CAAC,CAAC;QAC7D,CAAC;IACH,CAAC;IACD,OAAO,OAAO,CAAC;AACjB,CAAC;AAED,SAAS,uBAAuB,CAC9B,mBAAuC;AACvC,8DAA8D;AAC9D,iCAAyC;IAEzC,OAAO,mBAAmB;SACvB,MAAM,CAAC,CAAC,IAAI,EAAE,EAAE,CAAC,OAAO,IAAI,CAAC,OAAO,KAAK,QAAQ,CAAC;SAClD,GAAG,CAAC,CAAC,IAAI,EAAE,EAAE;QACZ,MAAM,cAAc,GAAI,IAAI,CAAC,OAAkB,CAAC,IAAI,EAAE,CAAC;QACvD,IAAI,cAAc,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;YAChC,OAAO,IAAI,CAAC;QACd,CAAC;QACD,MAAM,WAAW,GAAG,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,QAAQ,CAAC;YAChD,CAAC,CAAC,IAAI,CAAC,QAAQ,CAAC,iCAAiC,EAAE,IAAI,CAAC,QAAQ,CAAC;YACjE,CAAC,CAAC,IAAI,CAAC,QAAQ,CAAC;QAClB,OAAO,qBAAqB,WAAW,SAAS,cAAc,8BAA8B,WAAW,MAAM,CAAC;IAChH,CAAC,CAAC;SACD,MAAM,CAAC,CAAC,KAAK,EAAmB,EAAE,CAAC,KAAK,KAAK,IAAI,CAAC;SAClD,IAAI,CAAC,MAAM,CAAC,CAAC;AAClB,CAAC;AAED;;;GAGG;AACH,MAAM,CAAC,KAAK,UAAU,4BAA4B,CAChD,uBAA+B,EAC/B,SAAkB,EAClB,WAAiC,EACjC,4BAAsC,EAAE;IAExC,IAAI,SAAS;QACX,MAAM,CAAC,KAAK,CACV,+CAA+C,uBAAuB,EAAE,CACzE,CAAC;IACJ,iEAAiE;IACjE,wEAAwE;IACxE,MAAM,YAAY,GAAG,OAAO,EAAE,CAAC;IAC/B,MAAM,SAAS,GAAG,MAAM,2BAA2B,CACjD,uBAAuB,EACvB,YAAY,EACZ,SAAS,EACT,WAAW,EACX,yBAAyB,CAC1B,CAAC;IACF,IAAI,SAAS,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;QAC3B,IAAI,SAAS;YAAE,MAAM,CAAC,KAAK,CAAC,uCAAuC,CAAC,CAAC;QACrE,OAAO,EAAE,aAAa,EAAE,EAAE,EAAE,SAAS,EAAE,CAAC,EAAE,CAAC;IAC7C,CAAC;IACD,MAAM,iBAAiB,GAAG,MAAM,gBAAgB,CAAC,SAAS,EAAE,SAAS,CAAC,CAAC;IACvE,6DAA6D;IAC7D,MAAM,oBAAoB,GAAG,uBAAuB,CAClD,iBAAiB,EACjB,uBAAuB,CACxB,CAAC;IACF,IAAI,SAAS;QACX,MAAM,CAAC,KAAK,CACV,iCAAiC,oBAAoB,CAAC,MAAM,EAAE,CAC/D,CAAC;IACJ,IAAI,SAAS,IAAI,oBAAoB,CAAC,MAAM,GAAG,CAAC;QAC9C,MAAM,CAAC,KAAK,CACV,oCAAoC,oBAAoB,CAAC,SAAS,CAAC,CAAC,EAAE,GAAG,CAAC,KAAK,CAChF,CAAC;IACJ,OAAO,EAAE,aAAa,EAAE,oBAAoB,EAAE,SAAS,EAAE,SAAS,CAAC,MAAM,EAAE,CAAC;AAC9E,CAAC"}