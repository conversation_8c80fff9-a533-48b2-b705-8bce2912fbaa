/**
 * @license
 * Copyright 2025 Google LLC
 * SPDX-License-Identifier: Apache-2.0
 */
export var TelemetryTarget;
(function (TelemetryTarget) {
    TelemetryTarget["GCP"] = "gcp";
    TelemetryTarget["LOCAL"] = "local";
})(TelemetryTarget || (TelemetryTarget = {}));
const DEFAULT_TELEMETRY_TARGET = TelemetryTarget.LOCAL;
const DEFAULT_OTLP_ENDPOINT = 'http://localhost:4317';
export { DEFAULT_TELEMETRY_TARGET, DEFAULT_OTLP_ENDPOINT };
export { initializeTelemetry, shutdownTelemetry, isTelemetrySdkInitialized, } from './sdk.js';
export { logCliConfiguration, logUserPrompt, logToolCall, logApiRequest, logApiError, logApiResponse, } from './loggers.js';
export { StartSessionEvent, EndSessionEvent, UserPromptEvent, ToolCallEvent, ApiRequestEvent, ApiErrorEvent, ApiResponseEvent, } from './types.js';
export { SpanStatusCode, ValueType } from '@opentelemetry/api';
export { SemanticAttributes } from '@opentelemetry/semantic-conventions';
//# sourceMappingURL=index.js.map