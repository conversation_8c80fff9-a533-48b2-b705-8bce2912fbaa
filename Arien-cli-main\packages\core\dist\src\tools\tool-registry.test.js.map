{"version": 3, "file": "tool-registry.test.js", "sourceRoot": "", "sources": ["../../../src/tools/tool-registry.test.ts"], "names": [], "mappings": "AAAA;;;;GAIG;AAEH,uDAAuD;AACvD,OAAO,EACL,QAAQ,EACR,EAAE,EACF,MAAM,EACN,EAAE,EACF,UAAU,EACV,SAAS,GAEV,MAAM,QAAQ,CAAC;AAChB,OAAO,EAAE,YAAY,EAAE,cAAc,EAAE,MAAM,oBAAoB,CAAC;AAClE,OAAO,EAAE,iBAAiB,EAAE,MAAM,eAAe,CAAC;AAClD,OAAO,EACL,MAAM,EAGN,YAAY,GACb,MAAM,qBAAqB,CAAC;AAC7B,OAAO,EAAE,QAAQ,EAAc,MAAM,YAAY,CAAC;AAClD,OAAO,EAGL,SAAS,EACT,IAAI,GACL,MAAM,eAAe,CAAC;AACvB,OAAO,EAAE,QAAQ,EAAE,MAAM,oBAAoB,CAAC;AAE9C,sFAAsF;AACtF,MAAM,oBAAoB,GAAG,EAAE,CAAC,OAAO,CAAC,GAAG,EAAE,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC;AAEvD,0EAA0E;AAC1E,EAAE,CAAC,IAAI,CAAC,iBAAiB,EAAE,GAAG,EAAE,CAAC,CAAC;IAChC,gBAAgB,EAAE,oBAAoB;CACvC,CAAC,CAAC,CAAC;AAEJ,0BAA0B;AAC1B,EAAE,CAAC,IAAI,CAAC,oBAAoB,EAAE,KAAK,IAAI,EAAE;IACvC,MAAM,MAAM,GAAG,MAAM,EAAE,CAAC,YAAY,CAAC,oBAAoB,CAAC,CAAC;IAC3D,OAAO;QACL,GAAG,MAAM;QACT,QAAQ,EAAE,EAAE,CAAC,EAAE,EAAE;QACjB,KAAK,EAAE,EAAE,CAAC,EAAE,EAAE;KACf,CAAC;AACJ,CAAC,CAAC,CAAC;AAEH,qCAAqC;AACrC,MAAM,oBAAoB,GAAG,EAAE,CAAC,EAAE,EAAE,CAAC;AACrC,MAAM,oBAAoB,GAAG,EAAE,CAAC,EAAE,EAAE,CAAC;AACrC,MAAM,uBAAuB,GAAG,EAAE,CAAC,EAAE,EAAE,CAAC;AACxC,MAAM,qBAAqB,GAAG,EAAE,CAAC,EAAE,EAAE,CAAC;AAEtC,EAAE,CAAC,IAAI,CAAC,2CAA2C,EAAE,GAAG,EAAE;IACxD,MAAM,UAAU,GAAG,EAAE,CAAC,EAAE,EAAE,CAAC,kBAAkB,CAAC,GAAG,EAAE,CAAC,CAAC;QACnD,OAAO,EAAE,oBAAoB;QAC7B,IAAI,OAAO,CAAC,OAAY;YACtB,oBAAoB,CAAC,OAAO,CAAC,CAAC;QAChC,CAAC;QACD,sFAAsF;KACvF,CAAC,CAAC,CAAC;IACJ,OAAO,EAAE,MAAM,EAAE,UAAU,EAAE,CAAC;AAChC,CAAC,CAAC,CAAC;AAEH,EAAE,CAAC,IAAI,CAAC,2CAA2C,EAAE,GAAG,EAAE;IACxD,MAAM,wBAAwB,GAAG,EAAE,CAAC,EAAE,EAAE,CAAC,kBAAkB,CAAC,GAAG,EAAE,CAAC,CAAC;QACjE,MAAM,EAAE;YACN,EAAE,EAAE,EAAE,CAAC,EAAE,EAAE;SACZ;QACD,KAAK,EAAE,uBAAuB;KAC/B,CAAC,CAAC,CAAC;IACJ,OAAO,EAAE,oBAAoB,EAAE,wBAAwB,EAAE,CAAC;AAC5D,CAAC,CAAC,CAAC;AAEH,EAAE,CAAC,IAAI,CAAC,yCAAyC,EAAE,GAAG,EAAE;IACtD,MAAM,sBAAsB,GAAG,EAAE,CAAC,EAAE,EAAE,CAAC,kBAAkB,CAAC,GAAG,EAAE,CAAC,CAAC;QAC/D,KAAK,EAAE,qBAAqB;KAC7B,CAAC,CAAC,CAAC;IACJ,OAAO,EAAE,kBAAkB,EAAE,sBAAsB,EAAE,CAAC;AACxD,CAAC,CAAC,CAAC;AAEH,+BAA+B;AAC/B,EAAE,CAAC,IAAI,CAAC,eAAe,EAAE,KAAK,IAAI,EAAE;IAClC,MAAM,WAAW,GACf,MAAM,EAAE,CAAC,YAAY,CAAiC,eAAe,CAAC,CAAC;IACzE,OAAO;QACL,GAAG,WAAW;QACd,SAAS,EAAE,EAAE,CAAC,EAAE,EAAE,CAAC,kBAAkB,CAAC,GAAG,EAAE,CAAC,CAAC;YAC3C,8BAA8B;YAC9B,IAAI,EAAE,EAAE,CAAC,EAAE,EAAE,CAAC,iBAAiB,CAAC,EAAE,oBAAoB,EAAE,EAAE,EAAE,CAAC;YAC7D,QAAQ,EAAE,EAAE,CAAC,EAAE,EAAE;SAClB,CAAC,CAAC;KACJ,CAAC;AACJ,CAAC,CAAC,CAAC;AAEH,+DAA+D;AAC/D,MAAM,sBAAsB,GAAG,CAC7B,gBAAuC,EACjB,EAAE,CAAC,CAAC;IAC1B,IAAI,EAAE,EAAE,CAAC,EAAE,EAAE,CAAC,iBAAiB,CAAC,EAAE,oBAAoB,EAAE,gBAAgB,EAAE,CAAC;IAC3E,QAAQ,EAAE,EAAE,CAAC,EAAE,EAAE;CAClB,CAAC,CAAC;AAEH,MAAM,QAAS,SAAQ,QAAuC;IAC5D,YAAY,IAAI,GAAG,WAAW,EAAE,WAAW,GAAG,aAAa;QACzD,KAAK,CAAC,IAAI,EAAE,IAAI,EAAE,WAAW,EAAE;YAC7B,IAAI,EAAE,QAAQ;YACd,UAAU,EAAE;gBACV,KAAK,EAAE,EAAE,IAAI,EAAE,QAAQ,EAAE;aAC1B;YACD,QAAQ,EAAE,CAAC,OAAO,CAAC;SACpB,CAAC,CAAC;IACL,CAAC;IACD,KAAK,CAAC,OAAO,CAAC,MAAyB;QACrC,OAAO;YACL,UAAU,EAAE,iBAAiB,MAAM,CAAC,KAAK,EAAE;YAC3C,aAAa,EAAE,iBAAiB,MAAM,CAAC,KAAK,EAAE;SAC/C,CAAC;IACJ,CAAC;CACF;AAED,MAAM,gBAAgB,GAAqB;IACzC,GAAG,EAAE,MAAM;IACX,KAAK,EAAE,YAAY;IACnB,cAAc,EAAE,sBAAsB;IACtC,OAAO,EAAE,SAAS;IAClB,SAAS,EAAE,WAAW;IACtB,SAAS,EAAE,KAAK;IAChB,UAAU,EAAE,EAAE;IACR,gBAAgB,EAAE,CAAC;IACzB,YAAY,EAAE,YAAY,CAAC,OAAO;IAClC,SAAS,EAAE,iBAAiB;CAC7B,CAAC;AAEF,QAAQ,CAAC,cAAc,EAAE,GAAG,EAAE;IAC5B,IAAI,MAAc,CAAC;IACnB,IAAI,YAA0B,CAAC;IAE/B,UAAU,CAAC,GAAG,EAAE;QACd,MAAM,GAAG,IAAI,MAAM,CAAC,gBAAgB,CAAC,CAAC;QACtC,YAAY,GAAG,IAAI,YAAY,CAAC,MAAM,CAAC,CAAC;QACxC,EAAE,CAAC,KAAK,CAAC,OAAO,EAAE,MAAM,CAAC,CAAC,kBAAkB,CAAC,GAAG,EAAE,GAAE,CAAC,CAAC,CAAC;QACvD,EAAE,CAAC,KAAK,CAAC,OAAO,EAAE,OAAO,CAAC,CAAC,kBAAkB,CAAC,GAAG,EAAE,GAAE,CAAC,CAAC,CAAC;QACxD,EAAE,CAAC,KAAK,CAAC,OAAO,EAAE,OAAO,CAAC,CAAC,kBAAkB,CAAC,GAAG,EAAE,GAAE,CAAC,CAAC,CAAC;QACxD,EAAE,CAAC,KAAK,CAAC,OAAO,EAAE,KAAK,CAAC,CAAC,kBAAkB,CAAC,GAAG,EAAE,GAAE,CAAC,CAAC,CAAC;QAEtD,4BAA4B;QAC5B,oBAAoB,CAAC,SAAS,EAAE,CAAC,iBAAiB,CAAC,SAAS,CAAC,CAAC,CAAC,0BAA0B;QACzF,uBAAuB,CAAC,SAAS,EAAE,CAAC;QACpC,qBAAqB,CAAC,SAAS,EAAE,CAAC;QAClC,EAAE,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC,SAAS,EAAE,CAAC;QACjC,wEAAwE;QACxE,EAAE,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC,eAAe,CAAC,sBAAsB,CAAC,EAAE,CAAC,CAAC,CAAC;IACnE,CAAC,CAAC,CAAC;IAEH,SAAS,CAAC,GAAG,EAAE;QACb,EAAE,CAAC,eAAe,EAAE,CAAC;IACvB,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,cAAc,EAAE,GAAG,EAAE;QAC5B,EAAE,CAAC,4BAA4B,EAAE,GAAG,EAAE;YACpC,MAAM,IAAI,GAAG,IAAI,QAAQ,EAAE,CAAC;YAC5B,YAAY,CAAC,YAAY,CAAC,IAAI,CAAC,CAAC;YAChC,MAAM,CAAC,YAAY,CAAC,OAAO,CAAC,WAAW,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QACvD,CAAC,CAAC,CAAC;QACH,+BAA+B;IACjC,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,kBAAkB,EAAE,GAAG,EAAE;QAChC,EAAE,CAAC,gEAAgE,EAAE,GAAG,EAAE;YACxE,YAAY,CAAC,YAAY,CAAC,IAAI,QAAQ,EAAE,CAAC,CAAC,CAAC,iBAAiB;YAC5D,MAAM,CAAC,YAAY,CAAC,gBAAgB,CAAC,gBAAgB,CAAC,CAAC,CAAC,OAAO,CAAC,EAAE,CAAC,CAAC;QACtE,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,mDAAmD,EAAE,KAAK,IAAI,EAAE;YACjE,MAAM,WAAW,GAAG,gBAAgB,CAAC;YACrC,MAAM,WAAW,GAAG,gBAAgB,CAAC;YAErC,iDAAiD;YACjD,MAAM,YAAY,GAAG,EAAkB,CAAC,CAAC,wBAAwB;YACjE,MAAM,QAAQ,GAAG,IAAI,iBAAiB,CACpC,YAAY,EACZ,WAAW,EACX,8BAA8B,EAC9B,IAAI,EACJ,EAAE,EACF,iBAAiB,CAClB,CAAC;YACF,MAAM,QAAQ,GAAG,IAAI,iBAAiB,CACpC,YAAY,EACZ,WAAW,EACX,8BAA8B,EAC9B,IAAI,EACJ,EAAE,EACF,iBAAiB,CAClB,CAAC;YACF,MAAM,UAAU,GAAG,IAAI,QAAQ,CAAC,cAAc,CAAC,CAAC;YAEhD,YAAY,CAAC,YAAY,CAAC,QAAQ,CAAC,CAAC;YACpC,YAAY,CAAC,YAAY,CAAC,QAAQ,CAAC,CAAC;YACpC,YAAY,CAAC,YAAY,CAAC,UAAU,CAAC,CAAC;YAEtC,MAAM,gBAAgB,GAAG,YAAY,CAAC,gBAAgB,CAAC,WAAW,CAAC,CAAC;YACpE,MAAM,CAAC,gBAAgB,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC;YACzC,MAAM,CAAC,gBAAgB,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC;YACrD,MAAM,CAAE,gBAAgB,CAAC,CAAC,CAAuB,CAAC,UAAU,CAAC,CAAC,IAAI,CAChE,WAAW,CACZ,CAAC;YAEF,MAAM,gBAAgB,GAAG,YAAY,CAAC,gBAAgB,CAAC,WAAW,CAAC,CAAC;YACpE,MAAM,CAAC,gBAAgB,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC;YACzC,MAAM,CAAC,gBAAgB,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC;YACrD,MAAM,CAAE,gBAAgB,CAAC,CAAC,CAAuB,CAAC,UAAU,CAAC,CAAC,IAAI,CAChE,WAAW,CACZ,CAAC;YAEF,MAAM,CAAC,YAAY,CAAC,gBAAgB,CAAC,qBAAqB,CAAC,CAAC,CAAC,OAAO,CAAC,EAAE,CAAC,CAAC;QAC3E,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,eAAe,EAAE,GAAG,EAAE;QAC7B,IAAI,iCAA8D,CAAC;QACnE,IAAI,uBAAoD,CAAC;QACzD,IAAI,6BAA0D,CAAC;QAC/D,IAAI,YAA2D,CAAC;QAEhE,UAAU,CAAC,GAAG,EAAE;YACd,iCAAiC,GAAG,EAAE,CAAC,KAAK,CAC1C,MAAM,EACN,yBAAyB,CAC1B,CAAC;YACF,uBAAuB,GAAG,EAAE,CAAC,KAAK,CAAC,MAAM,EAAE,eAAe,CAAC,CAAC;YAC5D,6BAA6B,GAAG,EAAE,CAAC,KAAK,CAAC,MAAM,EAAE,qBAAqB,CAAC,CAAC;YACxE,YAAY,GAAG,EAAE,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC;YACnC,YAAY,GAAG,IAAI,YAAY,CAAC,MAAM,CAAC,CAAC,CAAC,iBAAiB;YAC1D,qEAAqE;YACrE,oBAAoB,CAAC,SAAS,EAAE,CAAC,iBAAiB,CAAC,SAAS,CAAC,CAAC;QAChE,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,+CAA+C,EAAE,KAAK,IAAI,EAAE;YAC7D,yCAAyC;YACzC,MAAM,gBAAgB,GAAG,sBAAsB,CAAC;YAChD,iCAAiC,CAAC,eAAe,CAAC,gBAAgB,CAAC,CAAC;YACpE,MAAM,oBAAoB,GAA0B;gBAClD;oBACE,IAAI,EAAE,mBAAmB;oBACzB,WAAW,EAAE,mBAAmB;oBAChC,UAAU,EAAE,EAAE,IAAI,EAAE,IAAI,CAAC,MAAM,EAAE,UAAU,EAAE,EAAE,EAAE;iBAClD;aACF,CAAC;YACF,YAAY,CAAC,eAAe,CAC1B,MAAM,CAAC,IAAI,CACT,IAAI,CAAC,SAAS,CAAC,CAAC,EAAE,qBAAqB,EAAE,oBAAoB,EAAE,CAAC,CAAC,CAClE,CACF,CAAC;YACF,MAAM,YAAY,CAAC,aAAa,EAAE,CAAC;YACnC,MAAM,CAAC,QAAQ,CAAC,CAAC,oBAAoB,CAAC,gBAAgB,CAAC,CAAC;YACxD,MAAM,cAAc,GAAG,YAAY,CAAC,OAAO,CAAC,mBAAmB,CAAC,CAAC;YACjE,MAAM,CAAC,cAAc,CAAC,CAAC,cAAc,CAAC,cAAc,CAAC,CAAC;QACxD,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,kEAAkE,EAAE,KAAK,IAAI,EAAE;YAChF,iCAAiC,CAAC,eAAe,CAAC,SAAS,CAAC,CAAC;YAC7D,6BAA6B,CAAC,eAAe,CAAC,SAAS,CAAC,CAAC;YACzD,MAAM,kBAAkB,GAAG;gBACzB,eAAe,EAAE;oBACf,OAAO,EAAE,gBAAgB;oBACzB,IAAI,EAAE,CAAC,QAAQ,EAAE,MAAM,CAAC;oBACxB,KAAK,EAAE,IAAI;iBACO;aACrB,CAAC;YACF,uBAAuB,CAAC,eAAe,CAAC,kBAAkB,CAAC,CAAC;YAE5D,MAAM,YAAY,CAAC,aAAa,EAAE,CAAC;YAEnC,MAAM,CAAC,oBAAoB,CAAC,CAAC,oBAAoB,CAC/C,kBAAkB,EAClB,SAAS,EACT,YAAY,CACb,CAAC;YACF,yDAAyD;YACzD,yDAAyD;YACzD,2CAA2C;YAC3C,sDAAsD;YACtD,+BAA+B;YAC/B,8BAA8B;YAC9B,6BAA6B;YAC7B,oBAAoB;YACpB,MAAM;YACN,mDAAmD;YAEnD,oFAAoF;YACpF,iEAAiE;YACjE,uDAAuD;QACzD,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,yEAAyE,EAAE,KAAK,IAAI,EAAE;YACvF,iCAAiC,CAAC,eAAe,CAAC,SAAS,CAAC,CAAC;YAC7D,uBAAuB,CAAC,eAAe,CAAC,EAAE,CAAC,CAAC;YAC5C,6BAA6B,CAAC,eAAe,CAC3C,kCAAkC,CACnC,CAAC;YAEF,MAAM,YAAY,CAAC,aAAa,EAAE,CAAC;YACnC,MAAM,CAAC,oBAAoB,CAAC,CAAC,oBAAoB,CAC/C,EAAE,EACF,kCAAkC,EAClC,YAAY,CACb,CAAC;QACJ,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,kFAAkF,EAAE,KAAK,IAAI,EAAE;YAChG,iCAAiC,CAAC,eAAe,CAAC,SAAS,CAAC,CAAC;YAC7D,uBAAuB,CAAC,eAAe,CAAC;gBACtC,aAAa,EAAE,EAAE,OAAO,EAAE,UAAU,EAAqB;aAC1D,CAAC,CAAC;YAEH,oBAAoB,CAAC,iBAAiB,CAAC,IAAI,KAAK,CAAC,mBAAmB,CAAC,CAAC,CAAC;YAEvE,MAAM,YAAY,CAAC,aAAa,EAAE,CAAC;YACnC,MAAM,CAAC,oBAAoB,CAAC,CAAC,oBAAoB,CAC/C;gBACE,aAAa,EAAE,EAAE,OAAO,EAAE,UAAU,EAAE;aACvC,EACD,SAAS,EACT,YAAY,CACb,CAAC;YACF,MAAM,CAAC,YAAY,CAAC,WAAW,EAAE,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC;QACrD,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IACH,oFAAoF;IACpF,gGAAgG;AAClG,CAAC,CAAC,CAAC"}