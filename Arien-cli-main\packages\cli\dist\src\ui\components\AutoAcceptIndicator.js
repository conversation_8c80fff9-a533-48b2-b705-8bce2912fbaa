import { jsxs as _jsxs, jsx as _jsx } from "react/jsx-runtime";
import { Box, Text } from 'ink';
import { Colors } from '../colors.js';
import { ApprovalMode } from '@arien/arien-cli-core';
export const AutoAcceptIndicator = ({ approvalMode, }) => {
    let textColor = '';
    let icon = '';
    let textContent = '';
    let subText = '';
    switch (approvalMode) {
        case ApprovalMode.AUTO_EDIT:
            textColor = Colors.AccentGreen;
            icon = '◉';
            textContent = 'Auto-Accept Edits';
            subText = ' (Shift+Tab)';
            break;
        case ApprovalMode.YOLO:
            textColor = Colors.AccentRed;
            icon = '⚡';
            textContent = 'YOLO Mode';
            subText = ' (Ctrl+Y)';
            break;
        case ApprovalMode.DEFAULT:
        default:
            break;
    }
    return (_jsxs(Box, { borderStyle: "round", borderColor: textColor, paddingX: 1, children: [_jsxs(Text, { color: textColor, bold: true, children: [icon, " ", textContent] }), subText && _jsx(Text, { color: Colors.Gray, dimColor: true, children: subText })] }));
};
//# sourceMappingURL=AutoAcceptIndicator.js.map