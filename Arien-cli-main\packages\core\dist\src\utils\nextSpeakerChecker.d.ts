/**
 * @license
 * Copyright 2025 Google LLC
 * SPDX-License-Identifier: Apache-2.0
 */
import { ArienClient } from '../core/client.js';
import { ArienChat } from '../core/arienChat.js';
export interface NextSpeakerResponse {
    reasoning: string;
    next_speaker: 'user' | 'model';
}
export declare function checkNextSpeaker(chat: ArienChat, arienClient: ArienClient, abortSignal: AbortSignal): Promise<NextSpeakerResponse | null>;
