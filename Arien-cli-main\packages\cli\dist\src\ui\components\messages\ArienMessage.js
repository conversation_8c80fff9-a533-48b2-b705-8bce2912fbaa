import { jsx as _jsx, jsxs as _jsxs } from "react/jsx-runtime";
import { Box } from 'ink';
import { MarkdownDisplay } from '../../utils/MarkdownDisplay.js';
import { Colors } from '../../colors.js';
import { AnimatedIcon } from '../shared/AnimatedIcon.js';
export const ArienMessage = ({ text, isPending, availableTerminalHeight, terminalWidth, }) => {
    // Calculate available width for content, accounting for icon and margins
    // Icon takes 1 character + marginRight of 1 = 2 characters total
    const contentWidth = Math.max(20, terminalWidth - 2);
    return (_jsxs(Box, { flexDirection: "row", children: [_jsx(Box, { marginRight: 1, children: _jsx(AnimatedIcon, { isPending: isPending, color: Colors.AccentPurple }) }), _jsx(Box, { flexGrow: 1, flexDirection: "column", children: _jsx(MarkdownDisplay, { text: text, isPending: isPending, availableTerminalHeight: availableTerminalHeight, terminalWidth: contentWidth }) })] }));
};
//# sourceMappingURL=ArienMessage.js.map