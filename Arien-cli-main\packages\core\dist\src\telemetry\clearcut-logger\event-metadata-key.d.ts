/**
 * @license
 * Copyright 2025 Google LLC
 * SPDX-License-Identifier: Apache-2.0
 */
export declare enum EventMetadataKey {
    ARIEN_CLI_KEY_UNKNOWN = 0,
    ARIEN_CLI_START_SESSION_MODEL = 1,
    ARIEN_CLI_START_SESSION_EMBEDDING_MODEL = 2,
    ARIEN_CLI_START_SESSION_SANDBOX = 3,
    ARIEN_CLI_START_SESSION_CORE_TOOLS = 4,
    ARIEN_CLI_START_SESSION_APPROVAL_MODE = 5,
    ARIEN_CLI_START_SESSION_API_KEY_ENABLED = 6,
    ARIEN_CLI_START_SESSION_VERTEX_API_ENABLED = 7,
    ARIEN_CLI_START_SESSION_DEBUG_MODE_ENABLED = 8,
    ARIEN_CLI_START_SESSION_MCP_SERVERS = 9,
    ARIEN_CLI_START_SESSION_TELEMETRY_ENABLED = 10,
    ARIEN_CLI_START_SESSION_TELEMETRY_LOG_USER_PROMPTS_ENABLED = 11,
    ARIEN_CLI_START_SESSION_RESPECT_GITIGNORE = 12,
    ARIEN_CLI_USER_PROMPT_LENGTH = 13,
    ARIEN_CLI_TOOL_CALL_NAME = 14,
    ARIEN_CLI_TOOL_CALL_DECISION = 15,
    ARIEN_CLI_TOOL_CALL_SUCCESS = 16,
    ARIEN_CLI_TOOL_CALL_DURATION_MS = 17,
    ARIEN_CLI_TOOL_ERROR_MESSAGE = 18,
    ARIEN_CLI_TOOL_CALL_ERROR_TYPE = 19,
    ARIEN_CLI_API_REQUEST_MODEL = 20,
    ARIEN_CLI_API_RESPONSE_MODEL = 21,
    ARIEN_CLI_API_RESPONSE_STATUS_CODE = 22,
    ARIEN_CLI_API_RESPONSE_DURATION_MS = 23,
    ARIEN_CLI_API_ERROR_MESSAGE = 24,
    ARIEN_CLI_API_RESPONSE_INPUT_TOKEN_COUNT = 25,
    ARIEN_CLI_API_RESPONSE_OUTPUT_TOKEN_COUNT = 26,
    ARIEN_CLI_API_RESPONSE_CACHED_TOKEN_COUNT = 27,
    ARIEN_CLI_API_RESPONSE_THINKING_TOKEN_COUNT = 28,
    ARIEN_CLI_API_RESPONSE_TOOL_TOKEN_COUNT = 29,
    ARIEN_CLI_API_ERROR_MODEL = 30,
    ARIEN_CLI_API_ERROR_TYPE = 31,
    ARIEN_CLI_API_ERROR_STATUS_CODE = 32,
    ARIEN_CLI_API_ERROR_DURATION_MS = 33,
    ARIEN_CLI_END_SESSION_ID = 34
}
export declare function getEventMetadataKey(keyName: string): EventMetadataKey | undefined;
