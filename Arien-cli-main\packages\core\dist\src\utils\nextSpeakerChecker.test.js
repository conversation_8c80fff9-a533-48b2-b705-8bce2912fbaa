/**
 * @license
 * Copyright 2025 Google LLC
 * SPDX-License-Identifier: Apache-2.0
 */
import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest';
import { ArienClient } from '../core/client.js';
import { Config } from '../config/config.js';
import { checkNextSpeaker } from './nextSpeakerChecker.js';
import { ArienChat } from '../core/arienChat.js';
// Mock ArienClient and Config constructor
vi.mock('../core/client.js');
vi.mock('../config/config.js');
// Define mocks for GoogleGenAI and Models instances that will be used across tests
const mockModelsInstance = {
    generateContent: vi.fn(),
    generateContentStream: vi.fn(),
    countTokens: vi.fn(),
    embedContent: vi.fn(),
    batchEmbedContents: vi.fn(),
};
const mockGoogleGenAIInstance = {
    getGenerativeModel: vi.fn().mockReturnValue(mockModelsInstance),
    // Add other methods of GoogleGenAI if they are directly used by ArienChat constructor or its methods
};
vi.mock('@google/genai', async () => {
    const actualGenAI = await vi.importActual('@google/genai');
    return {
        ...actualGenAI,
        GoogleGenAI: vi.fn(() => mockGoogleGenAIInstance), // Mock constructor to return the predefined instance
        // If Models is instantiated directly in ArienChat, mock its constructor too
        // For now, assuming Models instance is obtained via getGenerativeModel
    };
});
describe('checkNextSpeaker', () => {
    let chatInstance;
    let mockArienClient;
    let MockConfig;
    const abortSignal = new AbortController().signal;
    beforeEach(() => {
        MockConfig = vi.mocked(Config);
        const mockConfigInstance = new MockConfig('test-api-key', 'gemini-pro', false, '.', false, undefined, false, undefined, undefined, undefined);
        mockArienClient = new ArienClient(mockConfigInstance);
        // Reset mocks before each test to ensure test isolation
        vi.mocked(mockModelsInstance.generateContent).mockReset();
        vi.mocked(mockModelsInstance.generateContentStream).mockReset();
        // ArienChat will receive the mocked instances via the mocked GoogleGenAI constructor
        chatInstance = new ArienChat(mockConfigInstance, mockModelsInstance, // This is the instance returned by mockGoogleGenAIInstance.getGenerativeModel
        {}, []);
        // Spy on getHistory for chatInstance
        vi.spyOn(chatInstance, 'getHistory');
    });
    afterEach(() => {
        vi.clearAllMocks();
    });
    it('should return null if history is empty', async () => {
        chatInstance.getHistory.mockReturnValue([]);
        const result = await checkNextSpeaker(chatInstance, mockArienClient, abortSignal);
        expect(result).toBeNull();
        expect(mockArienClient.generateJson).not.toHaveBeenCalled();
    });
    it('should return null if the last speaker was the user', async () => {
        chatInstance.getHistory.mockReturnValue([
            { role: 'user', parts: [{ text: 'Hello' }] },
        ]);
        const result = await checkNextSpeaker(chatInstance, mockArienClient, abortSignal);
        expect(result).toBeNull();
        expect(mockArienClient.generateJson).not.toHaveBeenCalled();
    });
    it("should return { next_speaker: 'model' } when model intends to continue", async () => {
        chatInstance.getHistory.mockReturnValue([
            { role: 'model', parts: [{ text: 'I will now do something.' }] },
        ]);
        const mockApiResponse = {
            reasoning: 'Model stated it will do something.',
            next_speaker: 'model',
        };
        mockArienClient.generateJson.mockResolvedValue(mockApiResponse);
        const result = await checkNextSpeaker(chatInstance, mockArienClient, abortSignal);
        expect(result).toEqual(mockApiResponse);
        expect(mockArienClient.generateJson).toHaveBeenCalledTimes(1);
    });
    it("should return { next_speaker: 'user' } when model asks a question", async () => {
        chatInstance.getHistory.mockReturnValue([
            { role: 'model', parts: [{ text: 'What would you like to do?' }] },
        ]);
        const mockApiResponse = {
            reasoning: 'Model asked a question.',
            next_speaker: 'user',
        };
        mockArienClient.generateJson.mockResolvedValue(mockApiResponse);
        const result = await checkNextSpeaker(chatInstance, mockArienClient, abortSignal);
        expect(result).toEqual(mockApiResponse);
    });
    it("should return { next_speaker: 'user' } when model makes a statement", async () => {
        chatInstance.getHistory.mockReturnValue([
            { role: 'model', parts: [{ text: 'This is a statement.' }] },
        ]);
        const mockApiResponse = {
            reasoning: 'Model made a statement, awaiting user input.',
            next_speaker: 'user',
        };
        mockArienClient.generateJson.mockResolvedValue(mockApiResponse);
        const result = await checkNextSpeaker(chatInstance, mockArienClient, abortSignal);
        expect(result).toEqual(mockApiResponse);
    });
    it('should return null if arienClient.generateJson throws an error', async () => {
        const consoleWarnSpy = vi
            .spyOn(console, 'warn')
            .mockImplementation(() => { });
        chatInstance.getHistory.mockReturnValue([
            { role: 'model', parts: [{ text: 'Some model output.' }] },
        ]);
        mockArienClient.generateJson.mockRejectedValue(new Error('API Error'));
        const result = await checkNextSpeaker(chatInstance, mockArienClient, abortSignal);
        expect(result).toBeNull();
        consoleWarnSpy.mockRestore();
    });
    it('should return null if arienClient.generateJson returns invalid JSON (missing next_speaker)', async () => {
        chatInstance.getHistory.mockReturnValue([
            { role: 'model', parts: [{ text: 'Some model output.' }] },
        ]);
        mockArienClient.generateJson.mockResolvedValue({
            reasoning: 'This is incomplete.',
        }); // Type assertion to simulate invalid response
        const result = await checkNextSpeaker(chatInstance, mockArienClient, abortSignal);
        expect(result).toBeNull();
    });
    it('should return null if arienClient.generateJson returns a non-string next_speaker', async () => {
        chatInstance.getHistory.mockReturnValue([
            { role: 'model', parts: [{ text: 'Some model output.' }] },
        ]);
        mockArienClient.generateJson.mockResolvedValue({
            reasoning: 'Model made a statement, awaiting user input.',
            next_speaker: 123, // Invalid type
        });
        const result = await checkNextSpeaker(chatInstance, mockArienClient, abortSignal);
        expect(result).toBeNull();
    });
    it('should return null if arienClient.generateJson returns an invalid next_speaker string value', async () => {
        chatInstance.getHistory.mockReturnValue([
            { role: 'model', parts: [{ text: 'Some model output.' }] },
        ]);
        mockArienClient.generateJson.mockResolvedValue({
            reasoning: 'Model made a statement, awaiting user input.',
            next_speaker: 'neither', // Invalid enum value
        });
        const result = await checkNextSpeaker(chatInstance, mockArienClient, abortSignal);
        expect(result).toBeNull();
    });
});
//# sourceMappingURL=nextSpeakerChecker.test.js.map